package cn.iocoder.yudao.module.importer.service.importtask;

import cn.iocoder.yudao.module.importer.dal.dataobject.importtask.ImportTaskDO;

/**
 * 导入任务 Service 接口
 */
public interface ImportTaskService {

    /**
     * 创建导入任务
     *
     * @param userId 用户ID
     * @param accountBookId 账本ID
     * @return 任务ID
     */
    String createImportTask(Long userId, Long accountBookId);

    /**
     * 根据任务ID查询
     *
     * @param taskId 任务ID
     * @return 导入任务
     */
    ImportTaskDO getByTaskId(String taskId);

    /**
     * 根据唯一邮箱查询
     *
     * @param uniqueEmail 唯一邮箱
     * @return 导入任务
     */
    ImportTaskDO getByUniqueEmail(String uniqueEmail);

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 状态
     */
    void updateTaskStatus(String taskId, Integer status);

    /**
     * 更新任务状态和文件信息
     *
     * @param taskId 任务ID
     * @param status 状态
     * @param filePath 文件路径
     * @param fileName 文件名
     */
    void updateTaskStatusAndFile(String taskId, Integer status, String filePath, String fileName);

    /**
     * 更新解压密码
     *
     * @param taskId 任务ID
     * @param password 密码
     */
    void updateUnzipPassword(String taskId, String password);

    /**
     * 更新任务完成信息
     *
     * @param taskId 任务ID
     * @param status 状态
     * @param processedRecords 处理记录数
     * @param errorMessage 错误信息
     */
    void updateTaskCompleted(String taskId, Integer status, Integer processedRecords, String errorMessage);

}