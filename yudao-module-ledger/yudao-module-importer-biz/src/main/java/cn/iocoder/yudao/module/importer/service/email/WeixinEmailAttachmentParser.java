package cn.iocoder.yudao.module.importer.service.email;

import cn.iocoder.yudao.module.importer.service.parser.weixin.WeixinBillParser;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class WeixinEmailAttachmentParser extends EmailAttachmentParser {

    @Autowired
    private WeixinBillParser weixinBillParser;

    /**
     * 下载文件到本地
     *
     * @param sourceUrl
     * @param dir
     * @param prefix
     * @param fileName
     * @return
     */
    /**
     * 下载文件到本地
     *
     * @param sourceUrl 源URL
     * @param dir 目标目录
     * @param prefix 文件前缀
     * @param fileName 文件名
     * @return 本地文件路径
     */
    public String downloadFileByUrl(String sourceUrl, String dir, String prefix, String fileName) {
        File dirFile = new File(dir);
        if (!dirFile.exists()) {
            dirFile.mkdirs();
        }
        String localPath = dir + File.separator + prefix + "-" + fileName + ".zip";
        File file = new File(localPath);

        try {
            OkHttpClient okHttpClient = new OkHttpClient();
            Request request = new Request.Builder()
                    .url(sourceUrl)
                    .build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.code() == 200) {
                    InputStream stream = response.body().byteStream();
                    Files.copy(stream, file.toPath(), StandardCopyOption.REPLACE_EXISTING);
                    log.info("文件下载成功: {}", localPath);
                } else {
                    log.error("下载失败，HTTP状态码: {} - {}", response.code(), response.message());
                }
            }
        } catch (Exception e) {
            log.error("下载文件失败: {}", sourceUrl, e);
            // 即使下载失败，也返回本地路径，让调用方知道预期的文件位置
        }

        return localPath;
    }

    /**
     * 解析微信邮件内容，提取下载链接并下载文件
     *
     * @param emailContent 邮件内容
     * @return 下载的文件路径，如果没有找到下载链接则返回空字符串
     */
    @Override
    public String parse(String emailContent) {
        log.info("开始解析微信邮件内容");

        if (emailContent == null || emailContent.trim().isEmpty()) {
            log.warn("邮件内容为空");
            return "";
        }

        try {
            // 匹配微信邮件中的下载链接
            Pattern pattern = Pattern.compile("<a href=\"(.*?)\"(.*?)>(.*?)点击下载(.*?)</a>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
            Matcher matcher = pattern.matcher(emailContent);

            if (matcher.find()) {
                String url = matcher.group(1);
                log.info("提取到下载链接: {}", url);

                // 使用临时目录而不是硬编码路径
                String tempDir = System.getProperty("java.io.tmpdir");
                String downloadPath = downloadFileByUrl(url, tempDir, "weixin-bill", "download");
                log.info("文件下载完成: {}", downloadPath);
                return downloadPath;
            } else {
                log.warn("未找到微信账单下载链接");
                return "";
            }
        } catch (Exception e) {
            log.error("解析微信邮件内容失败", e);
            return "";
        }
    }

    /**
     * 解析下载的微信账单文件
     * @param filePath 文件路径
     * @return 解析结果摘要
     */
    public String parseWeixinBillFile(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                log.error("微信账单文件不存在: {}", filePath);
                return "文件不存在";
            }

            // 解析账单文件信息
            var billFileInfo = weixinBillParser.parseBillFileInfo(file);
            log.info("解析微信账单文件信息成功: {}", billFileInfo.getOriginalName());

            // 解析账单明细
            var billDetails = weixinBillParser.parseBillDetails(file);
            log.info("解析微信账单明细成功，共 {} 条记录", billDetails.size());

            // 转换为统一格式
            var accountBookItems = weixinBillParser.convertToAccountBookItems(billDetails, 1L, 1L);
            log.info("转换为统一格式成功，共 {} 条记录", accountBookItems.size());

            return String.format("解析成功：账户[%s]，时间范围[%s - %s]，共%d条记录",
                    billFileInfo.getNickname(),
                    billFileInfo.getStartTime(),
                    billFileInfo.getEndTime(),
                    billFileInfo.getCount());

        } catch (Exception e) {
            log.error("解析微信账单文件失败: {}", filePath, e);
            return "解析失败: " + e.getMessage();
        }
    }
}
