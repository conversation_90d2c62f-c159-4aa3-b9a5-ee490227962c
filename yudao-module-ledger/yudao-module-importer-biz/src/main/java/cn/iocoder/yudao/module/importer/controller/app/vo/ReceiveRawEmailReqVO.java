package cn.iocoder.yudao.module.importer.controller.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Schema(description = "内部接口 - 接收原始邮件的 Request VO")
@Data
public class ReceiveRawEmailReqVO {

    @Schema(description = "邮件服务器签名", requiredMode = Schema.RequiredMode.REQUIRED, example = "abc123def456")
    @NotBlank(message = "签名不能为空")
    private String signature;

    @Schema(description = "时间戳", requiredMode = Schema.RequiredMode.REQUIRED, example = "1634567890")
    @NotBlank(message = "时间戳不能为空")
    private String timestamp;

    @Schema(description = "原始邮件数据", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "原始邮件数据不能为空")
    private String rawEmailData;

}