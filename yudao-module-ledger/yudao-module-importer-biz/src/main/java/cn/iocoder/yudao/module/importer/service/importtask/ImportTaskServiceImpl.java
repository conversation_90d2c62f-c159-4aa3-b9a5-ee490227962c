package cn.iocoder.yudao.module.importer.service.importtask;

import cn.hutool.core.util.IdUtil;
import cn.iocoder.yudao.module.importer.dal.dataobject.importtask.ImportTaskDO;
import cn.iocoder.yudao.module.importer.dal.mysql.importtask.ImportTaskMapper;
import cn.iocoder.yudao.module.importer.enums.ImportTaskStatusEnum;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 导入任务 Service 实现类
 */
@Service
@Slf4j
public class ImportTaskServiceImpl implements ImportTaskService {

    @Resource
    private ImportTaskMapper importTaskMapper;

    @Override
    public String createImportTask(Long userId, Long accountBookId) {
        String taskId = IdUtil.fastSimpleUUID();
        String uniqueEmail = generateUniqueEmail(taskId);
        
        ImportTaskDO importTask = ImportTaskDO.builder()
                .taskId(taskId)
                .userId(userId)
                .accountBookId(accountBookId)
                .uniqueEmail(uniqueEmail)
                .status(ImportTaskStatusEnum.AWAITING_EMAIL.getStatus())
                .build();
        
        importTaskMapper.insert(importTask);
        log.info("创建导入任务成功, taskId: {}, uniqueEmail: {}", taskId, uniqueEmail);
        
        return taskId;
    }

    @Override
    public ImportTaskDO getByTaskId(String taskId) {
        return importTaskMapper.selectByTaskId(taskId);
    }

    @Override
    public ImportTaskDO getByUniqueEmail(String uniqueEmail) {
        return importTaskMapper.selectByUniqueEmail(uniqueEmail);
    }

    @Override
    public void updateTaskStatus(String taskId, Integer status) {
        ImportTaskDO updateObj = new ImportTaskDO();
        updateObj.setStatus(status);
        updateObj.setUpdateTime(LocalDateTime.now());
        
        importTaskMapper.update(updateObj, 
                new LambdaUpdateWrapper<ImportTaskDO>().eq(ImportTaskDO::getTaskId, taskId));
        
        log.info("更新任务状态成功, taskId: {}, status: {}", taskId, status);
    }

    @Override
    public void updateTaskStatusAndFile(String taskId, Integer status, String filePath, String fileName) {
        ImportTaskDO updateObj = new ImportTaskDO();
        updateObj.setStatus(status);
        updateObj.setFilePath(filePath);
        updateObj.setFileName(fileName);
        updateObj.setUpdateTime(LocalDateTime.now());
        
        importTaskMapper.update(updateObj, 
                new LambdaUpdateWrapper<ImportTaskDO>().eq(ImportTaskDO::getTaskId, taskId));
        
        log.info("更新任务状态和文件信息成功, taskId: {}, status: {}, filePath: {}", 
                taskId, status, filePath);
    }

    @Override
    public void updateUnzipPassword(String taskId, String password) {
        ImportTaskDO updateObj = new ImportTaskDO();
        updateObj.setUnzipPassword(password);
        updateObj.setUpdateTime(LocalDateTime.now());
        
        importTaskMapper.update(updateObj, 
                new LambdaUpdateWrapper<ImportTaskDO>().eq(ImportTaskDO::getTaskId, taskId));
        
        log.info("更新解压密码成功, taskId: {}", taskId);
    }

    @Override
    public void updateTaskCompleted(String taskId, Integer status, Integer processedRecords, String errorMessage) {
        ImportTaskDO updateObj = new ImportTaskDO();
        updateObj.setStatus(status);
        updateObj.setProcessedRecords(processedRecords);
        updateObj.setErrorMessage(errorMessage);
        updateObj.setProcessEndTime(LocalDateTime.now());
        updateObj.setUpdateTime(LocalDateTime.now());
        
        importTaskMapper.update(updateObj, 
                new LambdaUpdateWrapper<ImportTaskDO>().eq(ImportTaskDO::getTaskId, taskId));
        
        log.info("更新任务完成信息成功, taskId: {}, status: {}, processedRecords: {}", 
                taskId, status, processedRecords);
    }

    /**
     * 生成唯一邮箱地址
     */
    private String generateUniqueEmail(String taskId) {
        // 格式: import-{taskId}@yourdomain.com
        return String.format("<EMAIL>", taskId);
    }

}