package cn.iocoder.yudao.module.importer.mq.message;

import cn.iocoder.yudao.framework.mq.redis.core.stream.AbstractRedisStreamMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 邮件预处理消息
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MailPreprocessMessage extends AbstractRedisStreamMessage {

    public static final String TOPIC = "mail-preprocess";

    /**
     * 原始邮件数据
     */
    private String rawEmailData;

    @Override
    public String getStreamKey() {
        return TOPIC;
    }

}