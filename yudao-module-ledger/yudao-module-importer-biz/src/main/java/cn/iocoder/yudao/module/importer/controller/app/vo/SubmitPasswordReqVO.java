package cn.iocoder.yudao.module.importer.controller.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Schema(description = "app接口 - 提交解压密码请求 VO")
@Data
public class SubmitPasswordReqVO {

    @Schema(description = "解压密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @NotBlank(message = "解压密码不能为空")
    private String password;

}