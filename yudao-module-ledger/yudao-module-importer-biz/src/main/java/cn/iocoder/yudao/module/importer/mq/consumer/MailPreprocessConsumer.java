package cn.iocoder.yudao.module.importer.mq.consumer;

import cn.iocoder.yudao.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener;
import cn.iocoder.yudao.module.importer.mq.message.MailPreprocessMessage;
import cn.iocoder.yudao.module.importer.service.email.EmailProcessingService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MailPreprocessConsumer extends AbstractRedisStreamMessageListener<MailPreprocessMessage> {

    @Resource
    private EmailProcessingService emailProcessingService;

    @Override
    public void onMessage(MailPreprocessMessage message) {
        log.info("[MailPreprocessMessage][收到邮件预处理消息]");

        try {
            emailProcessingService.processRawEmail(message.getRawEmailData());
            log.info("[MailPreprocessMessage][邮件预处理完成]");
        } catch (Exception e) {
            log.error("[MailPreprocessMessage][邮件预处理失败]", e);
            throw e;
        }
    }
}