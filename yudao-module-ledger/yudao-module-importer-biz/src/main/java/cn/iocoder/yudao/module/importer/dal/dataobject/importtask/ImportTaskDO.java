package cn.iocoder.yudao.module.importer.dal.dataobject.importtask;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 导入任务 DO
 */
@TableName("importer_import_task")
@KeySequence("importer_import_task_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportTaskDO extends BaseDO {

    /**
     * 任务ID
     */
    @TableId
    private Long id;
    
    /**
     * 任务唯一标识
     */
    private String taskId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 账本ID
     */
    private Long accountBookId;
    
    /**
     * 生成的唯一邮箱地址
     */
    private String uniqueEmail;
    
    /**
     * 任务状态
     * @see cn.iocoder.yudao.module.importer.enums.ImportTaskStatusEnum
     */
    private Integer status;
    
    /**
     * 文件存储路径
     */
    private String filePath;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 解压密码
     */
    private String unzipPassword;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 处理开始时间
     */
    private LocalDateTime processStartTime;
    
    /**
     * 处理结束时间
     */
    private LocalDateTime processEndTime;
    
    /**
     * 处理的记录数
     */
    private Integer processedRecords;

}