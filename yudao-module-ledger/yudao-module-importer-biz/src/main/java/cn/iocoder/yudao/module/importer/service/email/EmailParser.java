package cn.iocoder.yudao.module.importer.service.email;

import jakarta.annotation.Resource;
import jakarta.mail.Address;
import jakarta.mail.MessagingException;
import jakarta.mail.Session;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.mail2.jakarta.util.MimeMessageParser;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Properties;

@Slf4j
@Component
public class EmailParser {

    @Resource
    private EmailAttachmentParserFactory attachmentParserFactory;

    private MimeMessageParser parser;

    public void parse(byte[] emailContent) {
        try {
            InputStream inputStream = new ByteArrayInputStream(emailContent);
            Properties props = new Properties();
            Session session = Session.getDefaultInstance(props, null);
            MimeMessage msg = new MimeMessage(session, inputStream);
            this.parser = new MimeMessageParser(msg).parse();


//            ReceivedEmailDTO dto = new ReceivedEmailDTO();
//            //邮件唯一id
//            String messageId = parser.getMimeMessage().getMessageID();
//            System.out.println(messageId);
//            //发件人
//            String from = parser.getFrom();
//            System.out.println(from);
//            //收件人列表
//            List<Address> toArray = parser.getTo();
//            String to = ((InternetAddress) toArray.get(0)).getAddress();
//            System.out.println(to);
//
//            //邮件发送时间
//            Date sendDate = parser.getMimeMessage().getSentDate();
//            System.out.println(sendDate);
//            //邮件主题
//            String subject = parser.getSubject();
//            System.out.println(subject);
//            //获取正文
//            String html = parser.getHtmlContent();
//
//            attachmentParser.parse(html);
//
//            //所有文件，包括附件和正文中的图片等文件
//            List<DataSource> dataSources = parser.getAttachmentList();
//            //获取不到html内容时，则获取非html文本内容
//            if (html == null || html.length() == 0) {
//                String plain = parser.getPlainContent();
//                System.out.println(plain);
//            } else {
//                //获取正文中的图片等文件
//                Collection<String> contentIds = parser.getContentIds();
//                for (String contentId : contentIds) {
//                    DataSource contentFile = parser.findAttachmentByCid(contentId);
//                    dataSources.remove(contentFile);
//                    String name = contentFile.getName();
//                    InputStream is = contentFile.getInputStream();
////                    FileUtils.copyInputStreamToFile(is, new File(dir + name));
////                    html = html.replace("cid:" + contentId, name);
//                }
////                FileUtils.writeStringToFile(new File(dir + "test_wx.html"), html, Charset.defaultCharset());
//            }
//            for (DataSource dataSource : dataSources) {
//                String name = dataSource.getName();
//                System.out.println(name);
//                InputStream is = dataSource.getInputStream();
////                FileUtils.copyInputStreamToFile(is, new File(dir + name));
//            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getFrom() throws MessagingException {
        return parser.getFrom();
    }

    public String getTo() throws MessagingException {
        List<Address> toArray = parser.getTo();
        return ((InternetAddress) toArray.get(0)).getAddress();
    }

    public Date getSendDate() throws MessagingException {
        return parser.getMimeMessage().getSentDate();
    }

    public Integer getEmailType() {
        return 1;
    }

    public String getAttachmentLocalPath() throws MessagingException {
        EmailAttachmentParser attachmentParser = attachmentParserFactory.getParser("weixin");
        return attachmentParser.parse(parser.getHtmlContent());
    }
}
