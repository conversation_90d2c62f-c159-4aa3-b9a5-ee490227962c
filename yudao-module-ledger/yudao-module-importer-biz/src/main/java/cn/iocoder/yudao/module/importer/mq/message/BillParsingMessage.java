package cn.iocoder.yudao.module.importer.mq.message;

import cn.iocoder.yudao.framework.mq.redis.core.stream.AbstractRedisStreamMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 账单解析消息
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BillParsingMessage extends AbstractRedisStreamMessage {

    public static final String TOPIC = "bill-parsing";

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * S3文件路径
     */
    private String s3Path;

    /**
     * 解压密码
     */
    private String password;

    @Override
    public String getStreamKey() {
        return TOPIC;
    }

}