package cn.iocoder.yudao.module.importer.controller.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "app接口 - 创建导入任务请求 VO")
@Data
public class CreateImportTaskReqVO {

    @Schema(description = "账本ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "账本ID不能为空")
    private Long accountBookId;

}