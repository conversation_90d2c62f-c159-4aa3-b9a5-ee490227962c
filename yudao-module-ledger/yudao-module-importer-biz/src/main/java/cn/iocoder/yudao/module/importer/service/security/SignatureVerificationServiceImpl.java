package cn.iocoder.yudao.module.importer.service.security;

import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;

/**
 * 签名验证服务实现类
 */
@Service
@Slf4j
public class SignatureVerificationServiceImpl implements SignatureVerificationService {

    @Value("${mail-server.secret-key:}")
    private String secretKey;

    @Value("${mail-server.signature.timestamp-tolerance-seconds:30}")
    private long timestampToleranceSeconds;

    @Value("${mail-server.signature.algorithm:HmacSHA256}")
    private String signatureAlgorithm;

    /**
     * 初始化时验证密钥配置
     */
    @PostConstruct
    public void init() {
        validateSecretKey();
    }

    /**
     * 验证密钥配置的安全性
     */
    private void validateSecretKey() {
        if (StrUtil.isBlank(secretKey)) {
            throw new IllegalStateException("邮件服务器签名密钥未配置，请设置 mail-server.secret-key 配置项");
        }

        // 检查密钥强度
        if (secretKey.length() < 32) {
            log.warn("邮件服务器签名密钥长度过短，建议使用至少32位的强密钥");
        }

        // 检查是否使用了常见的弱密钥
        String[] weakKeys = {"default", "test", "123456", "password", "secret", "key"};
        String lowerSecretKey = secretKey.toLowerCase();
        for (String weakKey : weakKeys) {
            if (lowerSecretKey.contains(weakKey)) {
                log.error("检测到弱密钥模式，请使用更强的密钥: {}", weakKey);
                throw new IllegalStateException("邮件服务器签名密钥强度不足，请使用更强的密钥");
            }
        }

        log.info("邮件服务器签名密钥验证通过，密钥长度: {} 位", secretKey.length());
    }

    @Override
    public boolean verifySignature(String signature, String timestamp, String data) {
        try {
            // 验证输入参数
            if (StrUtil.hasBlank(signature, timestamp, data)) {
                log.warn("签名验证参数不完整");
                return false;
            }

            // 验证时间戳格式和范围
            if (!isValidTimestamp(timestamp)) {
                return false;
            }

            // 计算预期签名
            String payload = buildPayload(timestamp, data);
            String expectedSignature = calculateSignature(payload);

            // 使用常量时间比较防止时序攻击
            boolean isValid = constantTimeEquals(expectedSignature, signature);
            if (!isValid) {
                log.warn("签名验证失败，请检查密钥配置和签名算法");
            }

            return isValid;
        } catch (Exception e) {
            log.error("签名验证异常", e);
            return false;
        }
    }

    /**
     * 验证时间戳的有效性
     */
    private boolean isValidTimestamp(String timestamp) {
        try {
            long currentTime = System.currentTimeMillis();
            long requestTime = Long.parseLong(timestamp);
            long timeDiff = Math.abs(currentTime - requestTime);

            if (timeDiff > timestampToleranceSeconds * 1000) {
                log.warn("请求时间戳超出允许范围: 当前时间={}, 请求时间={}, 差值={}秒",
                    currentTime, requestTime, timeDiff / 1000);
                return false;
            }

            return true;
        } catch (NumberFormatException e) {
            log.warn("时间戳格式无效: {}", timestamp);
            return false;
        }
    }

    /**
     * 构建签名载荷
     */
    private String buildPayload(String timestamp, String data) {
        // 使用更安全的载荷格式，添加分隔符防止拼接攻击
        return timestamp + "|" + data;
    }

    /**
     * 计算HMAC签名
     */
    private String calculateSignature(String payload) {
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA256, secretKey.getBytes(StandardCharsets.UTF_8));
        return HexUtil.encodeHexStr(hMac.digest(payload));
    }

    /**
     * 常量时间字符串比较，防止时序攻击
     */
    private boolean constantTimeEquals(String expected, String actual) {
        if (expected == null || actual == null) {
            return expected == actual;
        }

        if (expected.length() != actual.length()) {
            return false;
        }

        int result = 0;
        for (int i = 0; i < expected.length(); i++) {
            result |= expected.charAt(i) ^ actual.charAt(i);
        }

        return result == 0;
    }

}