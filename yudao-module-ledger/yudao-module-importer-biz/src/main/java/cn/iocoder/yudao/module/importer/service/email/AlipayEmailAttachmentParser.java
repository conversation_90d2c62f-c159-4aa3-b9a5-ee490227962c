package cn.iocoder.yudao.module.importer.service.email;

import cn.iocoder.yudao.module.importer.service.parser.alipay.AlipayBillParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * 支付宝邮件附件解析器
 * 支付宝邮件直接包含附件，不需要从邮件内容中提取下载链接
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AlipayEmailAttachmentParser extends EmailAttachmentParser {

    @Autowired
    private AlipayBillParser alipayBillParser;

    /**
     * 解析支付宝邮件内容
     * 支付宝邮件直接包含附件，此方法主要用于验证邮件内容
     *
     * @param emailContent 邮件内容
     * @return 解析结果（对于支付宝邮件，主要返回状态信息）
     */
    @Override
    public String parse(String emailContent) {
        log.info("开始解析支付宝邮件内容");

        try {
            // 支付宝邮件验证：检查是否包含支付宝相关关键词（不区分大小写）
            if (emailContent != null) {
                String lowerContent = emailContent.toLowerCase();
                if (lowerContent.contains("支付宝") ||
                    lowerContent.contains("alipay") ||
                    lowerContent.contains("交易流水明细")) {

                    log.info("支付宝邮件内容验证通过");
                    return "支付宝邮件验证成功";
                }
            }

            log.warn("邮件内容不符合支付宝邮件格式");
            return "邮件内容验证失败";
        } catch (Exception e) {
            log.error("解析支付宝邮件内容失败", e);
            return "解析失败: " + e.getMessage();
        }
    }

    /**
     * 解析下载的支付宝账单文件
     * @param filePath 文件路径
     * @return 解析结果摘要
     */
    public String parseAlipayBillFile(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                log.error("支付宝账单文件不存在: {}", filePath);
                return "文件不存在";
            }

            // 解析账单文件信息
            var billFileInfo = alipayBillParser.parseBillFileInfo(file);
            log.info("解析支付宝账单文件信息成功: {}", billFileInfo.getOriginalName());

            // 解析账单明细
            var billDetails = alipayBillParser.parseBillDetails(file);
            log.info("解析支付宝账单明细成功，共 {} 条记录", billDetails.size());

            // 转换为统一格式
            var accountBookItems = alipayBillParser.convertToAccountBookItems(billDetails, 1L, 1L);
            log.info("转换为统一格式成功，共 {} 条记录", accountBookItems.size());

            return String.format("解析成功：账户[%s]，时间范围[%s - %s]，共%d条记录",
                    billFileInfo.getNickname(),
                    billFileInfo.getStartTime(),
                    billFileInfo.getEndTime(),
                    billDetails.size());

        } catch (Exception e) {
            log.error("解析支付宝账单文件失败: {}", filePath, e);
            return "解析失败: " + e.getMessage();
        }
    }
}
