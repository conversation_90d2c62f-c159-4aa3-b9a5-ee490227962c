package cn.iocoder.yudao.module.importer.service.email;

import cn.iocoder.yudao.module.importer.dal.dataobject.importtask.ImportTaskDO;
import cn.iocoder.yudao.module.importer.enums.ImportTaskStatusEnum;
import cn.iocoder.yudao.module.importer.service.importtask.ImportTaskService;
import cn.iocoder.yudao.module.infra.service.file.FileService;
import jakarta.annotation.Resource;
import jakarta.mail.internet.MimeMessage;
import jakarta.mail.internet.MimeMultipart;
import jakarta.mail.BodyPart;
import jakarta.mail.MessagingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.mail.Session;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 邮件处理服务实现类
 */
@Service
@Slf4j
public class EmailProcessingServiceImpl implements EmailProcessingService {

    @Resource
    private ImportTaskService importTaskService;
    
    @Resource
    private FileService fileService;

    private static final Pattern EMAIL_PATTERN = Pattern.compile("import-([a-zA-Z0-9]+)@import\\.ledger\\.com");

    @Override
    public void processRawEmail(String rawEmailData) {
        try {
            // 解析邮件获取收件人地址
            String recipientEmail = extractRecipientEmail(rawEmailData);
            if (recipientEmail == null) {
                log.warn("无法提取收件人邮箱地址");
                return;
            }

            // 提取任务ID
            String taskId = extractTaskIdFromEmail(recipientEmail);
            if (taskId == null) {
                log.warn("无法从邮箱地址提取任务ID: {}", recipientEmail);
                return;
            }

            // 验证任务有效性
            ImportTaskDO task = importTaskService.getByTaskId(taskId);
            if (task == null) {
                log.warn("任务不存在: {}", taskId);
                return;
            }

            if (!ImportTaskStatusEnum.AWAITING_EMAIL.getStatus().equals(task.getStatus())) {
                log.warn("任务状态不正确: taskId={}, status={}", taskId, task.getStatus());
                return;
            }

            // 识别邮件类型（根据发件人）
            String emailType = identifyEmailType(rawEmailData);
            if (emailType == null) {
                log.warn("无法识别邮件类型: {}", taskId);
                importTaskService.updateTaskCompleted(taskId, ImportTaskStatusEnum.FAILED.getStatus(),
                        0, "无法识别邮件类型");
                return;
            }

            // 记录邮件类型信息（当前任务表设计中没有存储账单类型，这里只记录日志）
            log.info("识别到邮件类型: taskId={}, emailType={}", taskId, emailType);

            // 提取并上传附件
            String filePath = extractAndUploadAttachment(rawEmailData, taskId);
            if (filePath == null) {
                log.warn("未找到邮件附件: {}", taskId);
                importTaskService.updateTaskCompleted(taskId, ImportTaskStatusEnum.FAILED.getStatus(),
                        0, "未找到邮件附件");
                return;
            }

            // 更新任务状态
            importTaskService.updateTaskStatusAndFile(taskId,
                    ImportTaskStatusEnum.AWAITING_PASSWORD.getStatus(), filePath,
                    extractFileName(rawEmailData));

            log.info("邮件处理完成: taskId={}, filePath={}, emailType={}", taskId, filePath, emailType);

        } catch (Exception e) {
            log.error("处理邮件失败", e);
        }
    }

    private String extractRecipientEmail(String rawEmailData) {
        try {
            Session session = Session.getDefaultInstance(new Properties());
            MimeMessage message = new MimeMessage(session, new ByteArrayInputStream(rawEmailData.getBytes()));
            
            // 获取收件人地址
            if (message.getRecipients(jakarta.mail.Message.RecipientType.TO) != null && 
                message.getRecipients(jakarta.mail.Message.RecipientType.TO).length > 0) {
                return message.getRecipients(jakarta.mail.Message.RecipientType.TO)[0].toString();
            }
            
            return null;
        } catch (Exception e) {
            log.error("提取收件人邮箱失败", e);
            return null;
        }
    }

    private String extractTaskIdFromEmail(String email) {
        Matcher matcher = EMAIL_PATTERN.matcher(email);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 识别邮件类型（根据发件人）
     * @param rawEmailData 原始邮件数据
     * @return 邮件类型（weixin/alipay）
     */
    private String identifyEmailType(String rawEmailData) {
        try {
            Session session = Session.getDefaultInstance(new Properties());
            MimeMessage message = new MimeMessage(session, new ByteArrayInputStream(rawEmailData.getBytes()));

            // 获取发件人地址
            String fromAddress = "";
            if (message.getFrom() != null && message.getFrom().length > 0) {
                fromAddress = message.getFrom()[0].toString().toLowerCase();
            }

            log.info("邮件发件人: {}", fromAddress);

            // 根据发件人识别邮件类型
            if (fromAddress.contains("tenpay.com") || fromAddress.contains("weixin") || fromAddress.contains("wechat")) {
                return "weixin";
            } else if (fromAddress.contains("alipay.com") || fromAddress.contains("支付宝")) {
                return "alipay";
            }

            // 如果发件人无法识别，尝试从邮件主题识别
            String subject = message.getSubject();
            if (subject != null) {
                subject = subject.toLowerCase();
                if (subject.contains("微信") || subject.contains("weixin") || subject.contains("wechat")) {
                    return "weixin";
                } else if (subject.contains("支付宝") || subject.contains("alipay")) {
                    return "alipay";
                }
            }

            return null;
        } catch (Exception e) {
            log.error("识别邮件类型失败", e);
            return null;
        }
    }



    private String extractAndUploadAttachment(String rawEmailData, String taskId) {
        try {
            Session session = Session.getDefaultInstance(new Properties());
            MimeMessage message = new MimeMessage(session, new ByteArrayInputStream(rawEmailData.getBytes()));
            
            if (message.getContent() instanceof MimeMultipart) {
                MimeMultipart multipart = (MimeMultipart) message.getContent();
                
                for (int i = 0; i < multipart.getCount(); i++) {
                    BodyPart bodyPart = multipart.getBodyPart(i);
                    
                    // 检查是否为附件
                    if (BodyPart.ATTACHMENT.equalsIgnoreCase(bodyPart.getDisposition()) ||
                        (bodyPart.getFileName() != null && !bodyPart.getFileName().isEmpty())) {
                        
                        // 上传附件到文件服务
                        byte[] attachmentData = bodyPart.getInputStream().readAllBytes();
                        String fileName = bodyPart.getFileName();
                        
                        // 使用文件服务上传
                        String path = String.format("import-tasks/%s/%s", taskId, fileName);
                        String uploadResult = fileService.createFile(fileName, path, attachmentData);
                        
                        log.info("附件上传成功: taskId={}, fileName={}, path={}", taskId, fileName, uploadResult);
                        return uploadResult;
                    }
                }
            }
            
            return null;
        } catch (MessagingException | IOException e) {
            log.error("提取附件失败: taskId={}", taskId, e);
            return null;
        }
    }

    private String extractFileName(String rawEmailData) {
        try {
            Session session = Session.getDefaultInstance(new Properties());
            MimeMessage message = new MimeMessage(session, new ByteArrayInputStream(rawEmailData.getBytes()));
            
            if (message.getContent() instanceof MimeMultipart) {
                MimeMultipart multipart = (MimeMultipart) message.getContent();
                
                for (int i = 0; i < multipart.getCount(); i++) {
                    BodyPart bodyPart = multipart.getBodyPart(i);
                    
                    if (bodyPart.getFileName() != null && !bodyPart.getFileName().isEmpty()) {
                        return bodyPart.getFileName();
                    }
                }
            }
            
            return "unknown";
        } catch (Exception e) {
            log.error("提取文件名失败", e);
            return "unknown";
        }
    }

}