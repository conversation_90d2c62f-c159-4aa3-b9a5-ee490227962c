package cn.iocoder.yudao.module.importer.service.email;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class EmailAttachmentParserFactory {

    private static final String ALIPAY_EMAIL_TYPE = "alipay";
    private static final String WEIXIN_EMAIL_TYPE = "weixin";

    @Autowired
    private WeixinEmailAttachmentParser weixinEmailAttachmentParser;

    @Autowired
    private AlipayEmailAttachmentParser alipayEmailAttachmentParser;

    public EmailAttachmentParser getParser(String emailType) {
        return switch (emailType) {
            case "alipay" -> alipayEmailAttachmentParser;
            case "weixin" -> weixinEmailAttachmentParser;
            default -> throw new IllegalArgumentException("未知的邮件类型：" + emailType);
        };
    }
}
