package cn.iocoder.yudao.module.importer.service.billing;

import cn.iocoder.yudao.module.importer.dal.dataobject.importtask.ImportTaskDO;
import cn.iocoder.yudao.module.importer.enums.ImportTaskStatusEnum;
import cn.iocoder.yudao.module.importer.service.BillParsingService;
import cn.iocoder.yudao.module.importer.service.importtask.ImportTaskService;
import cn.iocoder.yudao.module.infra.service.file.FileService;
import cn.iocoder.yudao.module.ledger.api.accountbook.AccountBookItemApi;
import cn.iocoder.yudao.module.ledger.api.accountbook.dto.AccountBookItemCreateReqDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 账单处理服务实现类
 */
@Service
@Slf4j
public class BillProcessingServiceImpl implements BillProcessingService {

    @Resource
    private ImportTaskService importTaskService;

    @Resource
    private FileService fileService;

    @Resource
    private BillParsingService billParsingService;

    @Resource
    private AccountBookItemApi accountBookItemApi;

    @Override
    public void processBill(String taskId, String s3Path, String password) {
        ImportTaskDO task = null;
        Path tempDir = null;
        try {
            // 获取任务信息
            task = importTaskService.getByTaskId(taskId);
            if (task == null) {
                log.error("任务不存在: {}", taskId);
                return;
            }

            // 更新任务状态为处理中
            importTaskService.updateTaskStatus(taskId, ImportTaskStatusEnum.PROCESSING.getStatus());
            
            // 记录处理开始时间
            ImportTaskDO updateStartTime = new ImportTaskDO();
            updateStartTime.setTaskId(taskId);
            updateStartTime.setProcessStartTime(LocalDateTime.now());
            // TODO: 这里需要补充更新处理开始时间的逻辑

            // 从OSS下载完整文件
            Long configId = 24L; // 这里需要根据实际情况获取
            byte[] fileContent = fileService.getFileContent(configId, s3Path);
            
            if (fileContent == null || fileContent.length == 0) {
                throw new RuntimeException("下载文件失败或文件为空");
            }

            // 创建临时目录和文件
            tempDir = Files.createTempDirectory("bill-processing-" + taskId);
            Path zipFile = tempDir.resolve(task.getFileName());
            Files.write(zipFile, fileContent);

            // 解压文件
            Path extractDir = tempDir.resolve("extracted");
            Files.createDirectories(extractDir);
            
            try (ZipFile zip = new ZipFile(zipFile.toFile())) {
                if (zip.isEncrypted()) {
                    zip.setPassword(password.toCharArray());
                }
                zip.extractAll(extractDir.toString());
            }

            // 查找CSV文件并解析
            File[] csvFiles = extractDir.toFile().listFiles((dir, name) -> 
                name.toLowerCase().endsWith(".csv"));
            
            if (csvFiles == null || csvFiles.length == 0) {
                throw new RuntimeException("解压后未找到CSV文件");
            }

            int totalProcessed = 0;
            for (File csvFile : csvFiles) {
                log.info("开始解析CSV文件: {}", csvFile.getName());
                
                // 使用账单解析服务解析文件
                List<AccountBookItemCreateReqDTO> accountBookItems = 
                    billParsingService.parseBillFile(csvFile, task.getAccountBookId(), task.getUserId());
                
                if (accountBookItems != null && !accountBookItems.isEmpty()) {
                    // 批量创建账本记录
                    for (AccountBookItemCreateReqDTO item : accountBookItems) {
                        try {
                            accountBookItemApi.createAccountBookItem(item);
                            totalProcessed++;
                        } catch (Exception e) {
                            log.warn("创建账本记录失败: {}", item, e);
                        }
                    }
                }
            }

            // 更新任务完成状态
            importTaskService.updateTaskCompleted(taskId, ImportTaskStatusEnum.COMPLETED.getStatus(), 
                    totalProcessed, null);

            log.info("账单处理完成: taskId={}, processedRecords={}", taskId, totalProcessed);

        } catch (Exception e) {
            log.error("账单处理失败: taskId={}", taskId, e);
            
            // 更新任务失败状态
            if (task != null) {
                importTaskService.updateTaskCompleted(taskId, ImportTaskStatusEnum.FAILED.getStatus(), 
                        0, e.getMessage());
            }
            
            throw new RuntimeException("账单处理失败", e);
        } finally {
            // 清理临时文件
            if (tempDir != null) {
                try {
                    Files.walk(tempDir)
                         .sorted((a, b) -> b.compareTo(a)) // 反向排序，先删除文件再删除目录
                         .forEach(path -> {
                             try {
                                 Files.deleteIfExists(path);
                             } catch (Exception e) {
                                 log.warn("删除临时文件失败: {}", path, e);
                             }
                         });
                } catch (Exception e) {
                    log.warn("清理临时目录失败: {}", tempDir, e);
                }
            }
        }
    }

}