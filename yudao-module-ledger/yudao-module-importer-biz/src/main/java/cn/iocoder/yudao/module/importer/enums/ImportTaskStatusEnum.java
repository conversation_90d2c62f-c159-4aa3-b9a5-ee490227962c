package cn.iocoder.yudao.module.importer.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 导入任务状态枚举
 */
@AllArgsConstructor
@Getter
public enum ImportTaskStatusEnum {

    AWAITING_EMAIL(1, "等待邮件"),
    AWAITING_PASSWORD(2, "等待密码"),
    PROCESSING(3, "处理中"),
    COMPLETED(4, "已完成"),
    FAILED(5, "失败");

    /**
     * 状态值
     */
    private final Integer status;
    /**
     * 状态名
     */
    private final String name;

}