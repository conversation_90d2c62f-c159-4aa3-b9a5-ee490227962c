package cn.iocoder.yudao.module.importer.mq.consumer;

import cn.iocoder.yudao.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener;
import cn.iocoder.yudao.module.importer.mq.message.BillParsingMessage;
import cn.iocoder.yudao.module.importer.service.billing.BillProcessingService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BillParsingConsumer extends AbstractRedisStreamMessageListener<BillParsingMessage> {

    @Resource
    private BillProcessingService billProcessingService;

    @Override
    public void onMessage(BillParsingMessage message) {
        log.info("[BillParsingMessage][收到账单解析消息: taskId={}]", message.getTaskId());

        try {
            billProcessingService.processBill(message.getTaskId(), message.getS3Path(), message.getPassword());
            log.info("[BillParsingMessage][账单解析完成: taskId={}]", message.getTaskId());
        } catch (Exception e) {
            log.error("[BillParsingMessage][账单解析失败: taskId={}]", message.getTaskId(), e);
            throw e;
        }
    }
}