package cn.iocoder.yudao.module.importer.dal.mysql.importtask;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.importer.dal.dataobject.importtask.ImportTaskDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 导入任务 Mapper
 */
@Mapper
public interface ImportTaskMapper extends BaseMapperX<ImportTaskDO> {

    /**
     * 根据任务ID查询
     */
    default ImportTaskDO selectByTaskId(String taskId) {
        return selectOne(ImportTaskDO::getTaskId, taskId);
    }
    
    /**
     * 根据唯一邮箱查询
     */
    default ImportTaskDO selectByUniqueEmail(String uniqueEmail) {
        return selectOne(ImportTaskDO::getUniqueEmail, uniqueEmail);
    }

}