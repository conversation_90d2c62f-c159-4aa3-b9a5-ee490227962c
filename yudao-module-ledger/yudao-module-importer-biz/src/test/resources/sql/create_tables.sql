CREATE TABLE IF NOT EXISTS "ledger_bill_item" (
                                                  "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                  "bill_file_id" bigint NOT NULL,
                                                  "user_id" bigint NOT NULL,
                                                  "trade_time" varchar NOT NULL,
                                                  "trade_classification" varchar NOT NULL,
                                                  "counterparty" varchar NOT NULL,
                                                  "counterparty_account" varchar,
                                                  "goods_description" varchar NOT NULL,
                                                  "in_or_out" bit NOT NULL,
                                                  "amount" varchar NOT NULL,
                                                  "payment_mode" varchar NOT NULL,
                                                  "current_status" varchar NOT NULL,
                                                  "order_number" varchar,
                                                  "tenant_order_number" varchar,
                                                  "remark" varchar,
                                                  "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                  "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                  "deleted" bit NOT NULL DEFAULT FALSE,
                                                  "delete_time" datetime DEFAULT NULL,
                                                  "creator" varchar DEFAULT '',
                                                  "updater" varchar DEFAULT '',
                                                  PRIMARY KEY ("id")
) COMMENT '账单明细表';

-- 导入任务表
CREATE TABLE IF NOT EXISTS "importer_import_task" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "task_id" varchar(64) NOT NULL,
    "user_id" bigint NOT NULL,
    "account_book_id" bigint NOT NULL,
    "unique_email" varchar(255) NOT NULL,
    "status" tinyint NOT NULL,
    "file_path" varchar(512) DEFAULT NULL,
    "file_name" varchar(255) DEFAULT NULL,
    "unzip_password" varchar(255) DEFAULT NULL,
    "error_message" text DEFAULT NULL,
    "process_start_time" datetime DEFAULT NULL,
    "process_end_time" datetime DEFAULT NULL,
    "processed_records" int DEFAULT NULL,
    "creator" varchar(64) DEFAULT '',
    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    "tenant_id" bigint NOT NULL DEFAULT 0,
    PRIMARY KEY ("id")
);