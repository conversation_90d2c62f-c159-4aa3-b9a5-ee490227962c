-- 导入任务表
CREATE TABLE IF NOT EXISTS `importer_import_task` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
    `task_id` varchar(64) NOT NULL COMMENT '任务唯一标识',
    `user_id` bigint NOT NULL COMMENT '用户ID',
    `account_book_id` bigint NOT NULL COMMENT '账本ID',
    `unique_email` varchar(255) NOT NULL COMMENT '生成的唯一邮箱地址',
    `status` tinyint NOT NULL COMMENT '任务状态：1-等待邮件，2-等待密码，3-处理中，4-已完成，5-失败',
    `file_path` varchar(512) DEFAULT NULL COMMENT '文件存储路径',
    `file_name` varchar(255) DEFAULT NULL COMMENT '文件名',
    `unzip_password` varchar(255) DEFAULT NULL COMMENT '解压密码',
    `error_message` text DEFAULT NULL COMMENT '错误信息',
    `process_start_time` datetime DEFAULT NULL COMMENT '处理开始时间',
    `process_end_time` datetime DEFAULT NULL COMMENT '处理结束时间',
    `processed_records` int DEFAULT NULL COMMENT '处理的记录数',
    `creator` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_task_id` (`task_id`),
    UNIQUE KEY `uk_unique_email` (`unique_email`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='导入任务表';