package cn.iocoder.yudao.module.importer.service.security;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link SignatureVerificationServiceImpl} 的单元测试类
 */
public class SignatureVerificationServiceTest {

    private SignatureVerificationServiceImpl signatureVerificationService;
    private final String secretKey = "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"; // 64位强密钥

    @BeforeEach
    public void setUp() {
        signatureVerificationService = new SignatureVerificationServiceImpl();
        ReflectionTestUtils.setField(signatureVerificationService, "secretKey", secretKey);
        ReflectionTestUtils.setField(signatureVerificationService, "timestampToleranceSeconds", 30L);
        ReflectionTestUtils.setField(signatureVerificationService, "signatureAlgorithm", "HmacSHA256");
    }

    @Test
    public void testVerifySignature_Success() {
        // 准备参数
        String timestamp = String.valueOf(System.currentTimeMillis());
        String data = "test email data";
        
        // 生成正确的签名（使用新的载荷格式）
        String payload = timestamp + "|" + data;
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA256, secretKey.getBytes(StandardCharsets.UTF_8));
        String validSignature = HexUtil.encodeHexStr(hMac.digest(payload));

        // 调用并断言
        assertTrue(signatureVerificationService.verifySignature(validSignature, timestamp, data));
    }

    @Test
    public void testVerifySignature_InvalidSignature() {
        // 准备参数
        String timestamp = String.valueOf(System.currentTimeMillis());
        String data = "test email data";
        String invalidSignature = "invalid-signature";

        // 调用并断言
        assertFalse(signatureVerificationService.verifySignature(invalidSignature, timestamp, data));
    }

    @Test
    public void testVerifySignature_ExpiredTimestamp() {
        // 准备参数 - 使用过期的时间戳（60秒前，超过30秒窗口）
        String expiredTimestamp = String.valueOf(System.currentTimeMillis() - 60 * 1000);
        String data = "test email data";
        
        // 生成签名（使用新的载荷格式）
        String payload = expiredTimestamp + "|" + data;
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA256, secretKey.getBytes(StandardCharsets.UTF_8));
        String signature = HexUtil.encodeHexStr(hMac.digest(payload));

        // 调用并断言
        assertFalse(signatureVerificationService.verifySignature(signature, expiredTimestamp, data));
    }

    @Test
    public void testVerifySignature_InvalidTimestamp() {
        // 准备参数
        String invalidTimestamp = "invalid-timestamp";
        String data = "test email data";
        String signature = "some-signature";

        // 调用并断言
        assertFalse(signatureVerificationService.verifySignature(signature, invalidTimestamp, data));
    }

    @Test
    public void testVerifySignature_FutureTimestamp() {
        // 准备参数 - 使用未来的时间戳（60秒后，超过30秒窗口）
        String futureTimestamp = String.valueOf(System.currentTimeMillis() + 60 * 1000);
        String data = "test email data";
        
        // 生成签名（使用新的载荷格式）
        String payload = futureTimestamp + "|" + data;
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA256, secretKey.getBytes(StandardCharsets.UTF_8));
        String signature = HexUtil.encodeHexStr(hMac.digest(payload));

        // 调用并断言
        assertFalse(signatureVerificationService.verifySignature(signature, futureTimestamp, data));
    }

    @Test
    public void testVerifySignature_WithinTimeWindow() {
        // 准备参数 - 使用10秒前的时间戳（在30秒窗口内）
        String validTimestamp = String.valueOf(System.currentTimeMillis() - 10 * 1000);
        String data = "test email data";

        // 生成正确的签名
        String payload = validTimestamp + "|" + data;
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA256, secretKey.getBytes(StandardCharsets.UTF_8));
        String validSignature = HexUtil.encodeHexStr(hMac.digest(payload));

        // 调用并断言 - 应该验证成功
        assertTrue(signatureVerificationService.verifySignature(validSignature, validTimestamp, data));
    }

    @Test
    public void testVerifySignature_EmptyParameters() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String data = "test data";
        String signature = "valid-signature";

        // 测试空签名
        assertFalse(signatureVerificationService.verifySignature("", timestamp, data));
        assertFalse(signatureVerificationService.verifySignature(null, timestamp, data));

        // 测试空时间戳
        assertFalse(signatureVerificationService.verifySignature(signature, "", data));
        assertFalse(signatureVerificationService.verifySignature(signature, null, data));

        // 测试空数据
        assertFalse(signatureVerificationService.verifySignature(signature, timestamp, ""));
        assertFalse(signatureVerificationService.verifySignature(signature, timestamp, null));
    }

    @Test
    public void testWeakSecretKeyValidation() {
        // 测试弱密钥会在初始化时抛出异常
        SignatureVerificationServiceImpl weakKeyService = new SignatureVerificationServiceImpl();

        // 测试包含弱模式的密钥 - 这个会抛出异常
        ReflectionTestUtils.setField(weakKeyService, "secretKey", "this-is-a-default-secret-key-for-testing");
        ReflectionTestUtils.setField(weakKeyService, "timestampToleranceSeconds", 30L);
        assertThrows(IllegalStateException.class, weakKeyService::init);

        // 测试空密钥 - 这个会抛出异常
        SignatureVerificationServiceImpl emptyKeyService = new SignatureVerificationServiceImpl();
        ReflectionTestUtils.setField(emptyKeyService, "secretKey", "");
        ReflectionTestUtils.setField(emptyKeyService, "timestampToleranceSeconds", 30L);
        assertThrows(IllegalStateException.class, emptyKeyService::init);

        // 测试null密钥 - 这个会抛出异常
        SignatureVerificationServiceImpl nullKeyService = new SignatureVerificationServiceImpl();
        ReflectionTestUtils.setField(nullKeyService, "secretKey", null);
        ReflectionTestUtils.setField(nullKeyService, "timestampToleranceSeconds", 30L);
        assertThrows(IllegalStateException.class, nullKeyService::init);
    }

    @Test
    public void testConstantTimeComparison() {
        // 这个测试验证常量时间比较的正确性
        String timestamp = String.valueOf(System.currentTimeMillis());
        String data = "test data";

        // 生成正确的签名
        String payload = timestamp + "|" + data;
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA256, secretKey.getBytes(StandardCharsets.UTF_8));
        String validSignature = HexUtil.encodeHexStr(hMac.digest(payload));

        // 测试相同签名
        assertTrue(signatureVerificationService.verifySignature(validSignature, timestamp, data));

        // 测试不同长度的签名
        assertFalse(signatureVerificationService.verifySignature(validSignature + "extra", timestamp, data));
        assertFalse(signatureVerificationService.verifySignature(validSignature.substring(0, validSignature.length() - 1), timestamp, data));

        // 测试相同长度但不同内容的签名
        String wrongSignature = validSignature.substring(0, validSignature.length() - 1) + "x";
        assertFalse(signatureVerificationService.verifySignature(wrongSignature, timestamp, data));
    }

}