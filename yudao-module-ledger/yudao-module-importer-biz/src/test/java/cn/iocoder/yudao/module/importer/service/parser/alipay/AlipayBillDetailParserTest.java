package cn.iocoder.yudao.module.importer.service.parser.alipay;

import cn.iocoder.yudao.framework.test.core.ut.BaseMockitoUnitTest;
import cn.iocoder.yudao.module.importer.config.BillParsingConfig;
import cn.iocoder.yudao.module.importer.service.parser.BatchProcessor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.lenient;

/**
 * AlipayBillDetailParser 单元测试
 *
 * <AUTHOR>
 */
public class AlipayBillDetailParserTest extends BaseMockitoUnitTest {

    @InjectMocks
    private AlipayBillDetailParser parser;

    @Mock
    private BillParsingConfig billParsingConfig;

    private static final String TEST_FILE_PATH = "testdata/alipay/alipay_bill_detail_test.csv";
    private static final int EXPECTED_RECORD_COUNT = 70; // 根据CSV文件中的记录数

    // 缓存解析结果，避免重复解析
    private List<AlipayCsvData> cachedTestData;

    @BeforeEach
    public void setUp() {
        // 设置支付宝配置的默认值
        BillParsingConfig.AlipayConfig alipayConfig = new BillParsingConfig.AlipayConfig();
        alipayConfig.setCharset("UTF-8");  // 测试文件是UTF-8编码
        alipayConfig.setHeaderRowNum(25);
        alipayConfig.setBatchSize(500);

        lenient().when(billParsingConfig.getAlipay()).thenReturn(alipayConfig);
    }

    @Test
    public void testParseWithBatchProcessor() {
        // 获取测试文件
        URL resource = getClass().getClassLoader().getResource(TEST_FILE_PATH);
        assertNotNull(resource, "测试文件不存在: " + TEST_FILE_PATH);
        File testFile = new File(resource.getFile());
        assertTrue(testFile.exists(), "测试文件不存在: " + testFile.getAbsolutePath());

        // 创建批处理器收集数据
        List<AlipayCsvData> collectedData = new ArrayList<>();
        BatchProcessor<AlipayCsvData> processor = new BatchProcessor<AlipayCsvData>() {
            @Override
            public void processBatch(List<AlipayCsvData> batch) {
                collectedData.addAll(batch);
            }
        };

        // 执行解析
        assertDoesNotThrow(() -> {
            parser.parseWithBatchProcessor(testFile, processor);
        });

        // 验证解析结果
        assertNotNull(collectedData);
        assertTrue(collectedData.size() > 0, "应该解析出至少一条记录");
        
        // 缓存测试数据供其他测试使用
        cachedTestData = collectedData;
        
        // 验证第一条记录的数据
        if (!collectedData.isEmpty()) {
            AlipayCsvData firstRecord = collectedData.get(0);
            assertNotNull(firstRecord.getTradeTime(), "交易时间不应为空");
            assertNotNull(firstRecord.getTradeCategory(), "交易分类不应为空");
            assertNotNull(firstRecord.getInOrOut(), "收支类型不应为空");
            assertNotNull(firstRecord.getAmount(), "金额不应为空");
            
            System.out.println("解析的第一条记录:");
            System.out.println("交易时间: " + firstRecord.getTradeTime());
            System.out.println("交易分类: " + firstRecord.getTradeCategory());
            System.out.println("交易对方: " + firstRecord.getCounterparty());
            System.out.println("收/支: " + firstRecord.getInOrOut());
            System.out.println("金额: " + firstRecord.getAmount());
        }
    }

    @Test
    public void testParseWithBatchProcessorEmptyFile() {
        // 测试空文件或不存在的文件
        File nonExistentFile = new File("non_existent_file.csv");
        
        List<AlipayCsvData> collectedData = new ArrayList<>();
        BatchProcessor<AlipayCsvData> processor = batch -> collectedData.addAll(batch);

        // 应该抛出异常或处理错误
        assertThrows(RuntimeException.class, () -> {
            parser.parseWithBatchProcessor(nonExistentFile, processor);
        });
    }

    @Test
    public void testParseWithNullProcessor() {
        // 获取测试文件
        URL resource = getClass().getClassLoader().getResource(TEST_FILE_PATH);
        assertNotNull(resource);
        File testFile = new File(resource.getFile());

        // 测试null处理器
        assertThrows(Exception.class, () -> {
            parser.parseWithBatchProcessor(testFile, null);
        });
    }

    /**
     * 获取缓存的测试数据
     */
    private List<AlipayCsvData> getCachedTestData() {
        if (cachedTestData == null) {
            testParseWithBatchProcessor();
        }
        return cachedTestData;
    }

    @Test
    public void testDataIntegrity() {
        // 验证解析数据的完整性
        List<AlipayCsvData> testData = getCachedTestData();
        
        for (AlipayCsvData data : testData) {
            // 验证必要字段不为空
            assertNotNull(data.getTradeTime(), "交易时间不应为空");
            assertNotNull(data.getInOrOut(), "收支类型不应为空");
            assertNotNull(data.getAmount(), "金额不应为空");
            
            // 验证收支类型的值
            assertTrue(data.getInOrOut().equals("收入") || 
                      data.getInOrOut().equals("支出") || 
                      data.getInOrOut().equals("不计收支"),
                      "收支类型应为：收入、支出或不计收支，实际值：" + data.getInOrOut());
            
            // 验证金额格式
            String amount = data.getAmount();
            if (amount != null && !amount.trim().isEmpty()) {
                // 移除可能的货币符号后应该是数字
                String cleanAmount = amount.replaceAll("[¥￥\\s]", "");
                assertDoesNotThrow(() -> {
                    Double.parseDouble(cleanAmount);
                }, "金额格式不正确: " + amount);
            }
        }
    }
}
