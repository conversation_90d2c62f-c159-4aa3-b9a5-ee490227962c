package cn.iocoder.yudao.module.importer.service.security;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;

import java.nio.charset.StandardCharsets;

/**
 * 签名计算示例
 * 
 * 这个类展示了如何正确计算邮件服务器签名
 */
public class SignatureCalculationExample {

    public static void main(String[] args) {
        // 示例配置
        String secretKey = "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"; // 64位强密钥
        String timestamp = String.valueOf(System.currentTimeMillis()); // 当前时间戳（毫秒）
        String rawEmailData = "Received: from mail.example.com\nFrom: <EMAIL>\nSubject: Test Email\n\nEmail content here";

        // 计算签名
        String signature = calculateSignature(timestamp, rawEmailData, secretKey);

        // 输出结果
        System.out.println("=== 邮件服务器签名计算示例 ===");
        System.out.println("密钥: " + secretKey);
        System.out.println("时间戳: " + timestamp);
        System.out.println("邮件数据: " + rawEmailData.substring(0, Math.min(50, rawEmailData.length())) + "...");
        System.out.println("载荷: " + timestamp + "|" + rawEmailData.substring(0, Math.min(30, rawEmailData.length())) + "...");
        System.out.println("签名: " + signature);
        System.out.println();

        // 验证签名
        boolean isValid = verifySignature(signature, timestamp, rawEmailData, secretKey);
        System.out.println("签名验证结果: " + (isValid ? "✓ 有效" : "✗ 无效"));
        System.out.println();

        // 生成HTTP请求示例
        generateHttpRequestExample(signature, timestamp, rawEmailData);
    }

    /**
     * 计算HMAC-SHA256签名
     */
    public static String calculateSignature(String timestamp, String rawEmailData, String secretKey) {
        // 构建载荷：timestamp|rawEmailData
        String payload = timestamp + "|" + rawEmailData;
        
        // 使用HMAC-SHA256算法计算签名
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA256, secretKey.getBytes(StandardCharsets.UTF_8));
        
        // 返回十六进制字符串
        return HexUtil.encodeHexStr(hMac.digest(payload));
    }

    /**
     * 验证签名
     */
    public static boolean verifySignature(String signature, String timestamp, String rawEmailData, String secretKey) {
        String expectedSignature = calculateSignature(timestamp, rawEmailData, secretKey);
        return expectedSignature.equals(signature);
    }

    /**
     * 生成HTTP请求示例
     */
    private static void generateHttpRequestExample(String signature, String timestamp, String rawEmailData) {
        System.out.println("=== HTTP 请求示例 ===");
        System.out.println("POST /ledger/import/receive-raw-email");
        System.out.println("Content-Type: application/json");
        System.out.println();
        System.out.println("{");
        System.out.println("  \"signature\": \"" + signature + "\",");
        System.out.println("  \"timestamp\": \"" + timestamp + "\",");
        System.out.println("  \"rawEmailData\": \"" + rawEmailData.replace("\n", "\\n") + "\"");
        System.out.println("}");
        System.out.println();

        // Python 示例
        System.out.println("=== Python 计算示例 ===");
        System.out.println("import hmac");
        System.out.println("import hashlib");
        System.out.println();
        System.out.println("secret_key = \"your-secret-key\"");
        System.out.println("timestamp = \"" + timestamp + "\"");
        System.out.println("raw_email_data = \"your-email-data\"");
        System.out.println("payload = f\"{timestamp}|{raw_email_data}\"");
        System.out.println("signature = hmac.new(");
        System.out.println("    secret_key.encode('utf-8'),");
        System.out.println("    payload.encode('utf-8'),");
        System.out.println("    hashlib.sha256");
        System.out.println(").hexdigest()");
        System.out.println();

        // JavaScript 示例
        System.out.println("=== JavaScript 计算示例 ===");
        System.out.println("const crypto = require('crypto');");
        System.out.println();
        System.out.println("const secretKey = 'your-secret-key';");
        System.out.println("const timestamp = '" + timestamp + "';");
        System.out.println("const rawEmailData = 'your-email-data';");
        System.out.println("const payload = `${timestamp}|${rawEmailData}`;");
        System.out.println("const signature = crypto");
        System.out.println("  .createHmac('sha256', secretKey)");
        System.out.println("  .update(payload, 'utf8')");
        System.out.println("  .digest('hex');");
    }
}
