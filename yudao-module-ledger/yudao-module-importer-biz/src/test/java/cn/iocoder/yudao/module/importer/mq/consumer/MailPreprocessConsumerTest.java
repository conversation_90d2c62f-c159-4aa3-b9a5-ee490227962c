package cn.iocoder.yudao.module.importer.mq.consumer;

import cn.iocoder.yudao.module.importer.mq.message.MailPreprocessMessage;
import cn.iocoder.yudao.module.importer.service.email.EmailProcessingService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * {@link MailPreprocessConsumer} 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class MailPreprocessConsumerTest {

    @Mock
    private EmailProcessingService emailProcessingService;

    @InjectMocks
    private MailPreprocessConsumer mailPreprocessConsumer;

    @Test
    public void testOnMessage_Success() {
        // 准备参数
        MailPreprocessMessage message = new MailPreprocessMessage();
        message.setRawEmailData("test raw email data");

        // 调用
        assertDoesNotThrow(() -> mailPreprocessConsumer.onMessage(message));

        // 验证调用
        verify(emailProcessingService).processRawEmail("test raw email data");
    }

    @Test
    public void testOnMessage_ProcessingException() {
        // 准备参数
        MailPreprocessMessage message = new MailPreprocessMessage();
        message.setRawEmailData("test raw email data");

        // mock 异常
        RuntimeException exception = new RuntimeException("Processing failed");
        doThrow(exception).when(emailProcessingService).processRawEmail(anyString());

        // 调用并断言异常
        RuntimeException thrown = assertThrows(RuntimeException.class, 
                () -> mailPreprocessConsumer.onMessage(message));
        assertEquals("Processing failed", thrown.getMessage());

        // 验证调用
        verify(emailProcessingService).processRawEmail("test raw email data");
    }

}