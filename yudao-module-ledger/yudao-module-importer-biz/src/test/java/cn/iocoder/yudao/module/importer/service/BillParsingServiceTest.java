package cn.iocoder.yudao.module.importer.service;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.importer.config.BillParsingConfig;
import cn.iocoder.yudao.module.importer.service.parser.BillParserFactory;
import cn.iocoder.yudao.module.importer.service.parser.alipay.AlipayBillDetailParser;
import cn.iocoder.yudao.module.importer.service.parser.alipay.AlipayBillParser;
import cn.iocoder.yudao.module.importer.service.parser.alipay.AlipayBillFileInfoParser;
import cn.iocoder.yudao.module.importer.service.parser.alipay.AlipayBillItemConverter;
import cn.iocoder.yudao.module.importer.service.parser.weixin.WeixinBillParser;
import cn.iocoder.yudao.module.importer.service.parser.weixin.WeixinBillDetailParser;
import cn.iocoder.yudao.module.importer.service.parser.weixin.WeixinBillFileInfoParser;
import cn.iocoder.yudao.module.importer.service.parser.weixin.WeixinBillItemConverter;
import cn.iocoder.yudao.module.ledger.api.accountbook.AccountBookItemApi;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import jakarta.annotation.Resource;
import java.io.File;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 账单解析服务测试类
 *
 * <AUTHOR>
 */
@Import({BillParsingService.class, BillParserFactory.class, AlipayBillParser.class, AlipayBillDetailParser.class, AlipayBillFileInfoParser.class, AlipayBillItemConverter.class, WeixinBillParser.class, WeixinBillDetailParser.class, WeixinBillFileInfoParser.class, WeixinBillItemConverter.class, BillParsingConfig.class})
public class BillParsingServiceTest extends BaseDbUnitTest {

    @MockBean
    private AccountBookItemApi accountBookItemApi;
    
    @Resource
    private BillParsingService billParsingService;
    
    @Resource
    private BillParserFactory billParserFactory;
    
    @Test
    public void testGetSupportedBillTypes() {
        List<String> supportedTypes = billParsingService.getSupportedBillTypes();

        assertNotNull(supportedTypes);
        assertFalse(supportedTypes.isEmpty());

        // 验证包含预期的账单类型
        assertTrue(supportedTypes.contains("WEIXIN"));
        assertTrue(supportedTypes.contains("ALIPAY"));

        System.out.println("支持的账单类型: " + supportedTypes);
    }
    
    @Test
    public void testParserFactory() {
        // 测试工厂是否正确初始化
        assertNotNull(billParserFactory);
        
        // 测试获取微信解析器
        assertDoesNotThrow(() -> {
            billParserFactory.getParser("WEIXIN");
        });
        
        // 测试获取支付宝解析器
        assertDoesNotThrow(() -> {
            billParserFactory.getParser("ALIPAY");
        });
        
        // 测试不支持的类型
        assertThrows(IllegalArgumentException.class, () -> {
            billParserFactory.getParser("UNSUPPORTED_TYPE");
        });
    }
    
    @Test
    public void testCanParseByFileName() {
        // 创建模拟文件进行测试
        File weixinFile = new File("微信支付账单(20240101-20240131).csv");
        File alipayFile = new File("alipay_bill_20240101_20240131.csv");

        // 注意：这些文件不存在，所以canParse会返回false
        // 但我们可以测试解析器的文件名匹配逻辑

        // 测试微信文件名匹配
        assertTrue(billParserFactory.getParser("WEIXIN").canParse(weixinFile) || !weixinFile.exists());

        // 测试支付宝文件名匹配
        assertTrue(billParserFactory.getParser("ALIPAY").canParse(alipayFile) || !alipayFile.exists());
    }
    
    @Test
    public void testBillParsingServiceConfiguration() {
        // 测试服务是否正确配置
        assertNotNull(billParsingService);
        
        // 测试获取支持的类型
        List<String> types = billParsingService.getSupportedBillTypes();
        assertNotNull(types);
        assertFalse(types.isEmpty());
        
        System.out.println("账单解析服务配置正常，支持的类型数量: " + types.size());
    }
}
