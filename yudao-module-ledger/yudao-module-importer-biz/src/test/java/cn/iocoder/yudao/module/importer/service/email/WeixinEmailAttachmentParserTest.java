package cn.iocoder.yudao.module.importer.service.email;

import cn.iocoder.yudao.framework.test.core.ut.BaseMockitoUnitTest;
import cn.iocoder.yudao.module.importer.service.parser.weixin.WeixinBillParser;
import okhttp3.*;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * WeixinEmailAttachmentParser 单元测试
 *
 * <AUTHOR>
 */
public class WeixinEmailAttachmentParserTest extends BaseMockitoUnitTest {

    @InjectMocks
    private WeixinEmailAttachmentParser parser;

    @Mock
    private WeixinBillParser weixinBillParser;

    @Test
    public void testParseValidWeixinEmail() {
        // 测试有效的微信邮件内容，包含下载链接
        String emailContent = "<html><body>" +
                "<p>您好，您的微信支付账单已生成。</p>" +
                "<a href=\"https://download.weixin.com/bill/test123.zip\">点击下载账单</a>" +
                "</body></html>";

        // 由于网络请求会失败，我们主要验证能够提取到下载链接
        String result = parser.parse(emailContent);

        // 验证能够提取到下载链接并尝试下载（即使下载失败也会返回本地路径）
        assertNotNull(result);
        assertTrue(result.contains("weixin-bill"));
        assertTrue(result.contains("download"));
    }

    @Test
    public void testParseEmailWithoutDownloadLink() {
        // 测试不包含下载链接的邮件内容
        String emailContent = "<html><body>" +
                "<p>您好，这是一封普通的邮件，不包含下载链接。</p>" +
                "</body></html>";

        String result = parser.parse(emailContent);
        
        // 没有找到下载链接，应该返回空字符串
        assertEquals("", result);
    }

    @Test
    public void testParseEmailWithInvalidDownloadLink() {
        // 测试包含无效下载链接的邮件内容
        String emailContent = "<html><body>" +
                "<p>您好，您的微信支付账单已生成。</p>" +
                "<a href=\"invalid-url\">点击下载账单</a>" +
                "</body></html>";

        String result = parser.parse(emailContent);

        // 即使链接无效，也应该尝试下载并返回本地路径
        assertNotNull(result);
        assertTrue(result.contains("weixin-bill"));
    }

    @Test
    public void testParseNullEmail() {
        // 测试null邮件内容
        String result = parser.parse(null);
        
        assertEquals("", result);
    }

    @Test
    public void testParseEmptyEmail() {
        // 测试空邮件内容
        String result = parser.parse("");
        
        assertEquals("", result);
    }

    @Test
    public void testParseEmailWithMultipleLinks() {
        // 测试包含多个链接的邮件内容，应该只处理第一个匹配的下载链接
        String emailContent = "<html><body>" +
                "<p>您好，您的微信支付账单已生成。</p>" +
                "<a href=\"https://download.weixin.com/bill/test1.zip\">点击下载账单1</a>" +
                "<a href=\"https://download.weixin.com/bill/test2.zip\">点击下载账单2</a>" +
                "</body></html>";

        String result = parser.parse(emailContent);

        assertNotNull(result);
        assertTrue(result.contains("weixin-bill"));
        // 应该处理第一个匹配的链接
    }

    @Test
    public void testParseWeixinBillFileNonExistentFile() {
        // 测试解析不存在的文件
        String nonExistentFilePath = "/path/to/non/existent/file.csv";
        
        String result = parser.parseWeixinBillFile(nonExistentFilePath);
        
        assertNotNull(result);
        assertEquals("文件不存在", result);
    }

    @Test
    public void testParseWeixinBillFileWithValidFile() {
        // 由于parseWeixinBillFile方法依赖真实文件和WeixinBillParser，
        // 这里主要测试文件不存在的情况
        // 真实文件的测试应该在集成测试中进行
        
        // 创建一个临时文件进行测试
        String tempFilePath = System.getProperty("java.io.tmpdir") + "/test_weixin_bill.csv";
        
        // 由于文件不存在，应该返回文件不存在的消息
        String result = parser.parseWeixinBillFile(tempFilePath);
        
        assertNotNull(result);
        assertEquals("文件不存在", result);
    }

    @Test
    public void testDownloadFileByUrlPathGeneration() {
        // 测试下载文件路径生成功能（不实际下载）
        String sourceUrl = "https://example.com/test.zip";
        String dir = System.getProperty("java.io.tmpdir");
        String prefix = "test";
        String fileName = "bill";

        // 测试路径生成逻辑
        String result = parser.downloadFileByUrl(sourceUrl, dir, prefix, fileName);
        assertNotNull(result);
        assertTrue(result.contains(dir));
        assertTrue(result.contains(prefix));
        assertTrue(result.contains(fileName));
        assertTrue(result.endsWith(".zip"));
    }

    @Test
    public void testDownloadFileByUrlCreateDirectory() {
        // 测试下载文件时创建目录的功能
        String sourceUrl = "https://example.com/test.zip";
        String dir = System.getProperty("java.io.tmpdir") + "/test_download_dir";
        String prefix = "test";
        String fileName = "bill";

        // 确保目录不存在
        File dirFile = new File(dir);
        if (dirFile.exists()) {
            dirFile.delete();
        }

        // 测试目录创建
        String result = parser.downloadFileByUrl(sourceUrl, dir, prefix, fileName);
        assertNotNull(result);

        // 验证目录被创建
        assertTrue(dirFile.exists());
        assertTrue(dirFile.isDirectory());

        // 清理测试目录
        dirFile.delete();
    }

    @Test
    public void testParseEmailWithCaseInsensitivePattern() {
        // 测试大小写不敏感的模式匹配
        String emailContent1 = "<html><body>" +
                "<A HREF=\"https://download.weixin.com/bill/test.zip\">点击下载</A>" +
                "</body></html>";

        String emailContent2 = "<html><body>" +
                "<a href=\"https://download.weixin.com/bill/test.zip\">点击下载账单文件</a>" +
                "</body></html>";

        String result1 = parser.parse(emailContent1);
        String result2 = parser.parse(emailContent2);

        assertNotNull(result1);
        assertNotNull(result2);
        assertTrue(result1.contains("weixin-bill"));
        assertTrue(result2.contains("weixin-bill"));
    }

    @Test
    public void testParseEmailWithComplexHtml() {
        // 测试复杂HTML结构的邮件
        String emailContent = "<html><head><title>微信支付账单</title></head><body>" +
                "<div class=\"header\">微信支付</div>" +
                "<div class=\"content\">" +
                "<p>尊敬的用户，您的账单已生成。</p>" +
                "<div class=\"download-section\">" +
                "<a href=\"https://download.weixin.com/bill/complex123.zip\" class=\"download-btn\">" +
                "点击下载您的账单文件</a>" +
                "</div>" +
                "</div>" +
                "</body></html>";

        String result = parser.parse(emailContent);

        assertNotNull(result);
        assertTrue(result.contains("weixin-bill"));
    }
}
