package cn.iocoder.yudao.module.importer.service.parser.alipay;

import cn.iocoder.yudao.framework.test.core.ut.BaseMockitoUnitTest;
import cn.iocoder.yudao.module.importer.config.BillParsingConfig;
import cn.iocoder.yudao.module.importer.dal.dataobject.billfile.BillFileInfoDO;
import cn.iocoder.yudao.module.ledger.api.accountbook.dto.AccountBookItemCreateReqDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * AlipayBillParser 集成测试
 * 测试完整的支付宝账单解析流程
 *
 * <AUTHOR>
 */
public class AlipayBillParserIntegrationTest extends BaseMockitoUnitTest {

    @InjectMocks
    private AlipayBillParser parser;

    @Mock
    private AlipayBillFileInfoParser fileInfoParser;

    @Mock
    private AlipayBillDetailParser detailParser;

    @Mock
    private AlipayBillItemConverter itemConverter;

    @Mock
    private BillParsingConfig billParsingConfig;

    @BeforeEach
    public void setUp() {
        // 设置支付宝配置
        BillParsingConfig.AlipayConfig alipayConfig = new BillParsingConfig.AlipayConfig();
        alipayConfig.setCharset("GBK");
        alipayConfig.setHeaderRowNum(25);
        alipayConfig.setBatchSize(500);

        lenient().when(billParsingConfig.getAlipay()).thenReturn(alipayConfig);
    }

    @Test
    public void testCompleteAlipayBillParsingFlow() throws IOException {
        // 创建临时的支付宝账单文件
        File tempFile = createTempAlipayBillFile();
        
        try {
            // 1. 测试文件识别
            assertTrue(parser.canParse(tempFile), "应该能够识别支付宝账单文件");
            assertEquals("ALIPAY", parser.getSupportedBillType());

            // 2. 测试文件信息解析 - Mock返回值
            BillFileInfoDO mockFileInfo = BillFileInfoDO.builder()
                    .type("ALIPAY")
                    .originalName(tempFile.getName())
                    .nickname("测试用户")
                    .build();
            when(fileInfoParser.parse(tempFile)).thenReturn(mockFileInfo);

            BillFileInfoDO fileInfo = parser.parseBillFileInfo(tempFile);
            assertNotNull(fileInfo);
            assertEquals("ALIPAY", fileInfo.getType());
            assertEquals(tempFile.getName(), fileInfo.getOriginalName());

            // 3. 测试明细解析 - Mock批处理器
            doAnswer(invocation -> {
                // 模拟批处理器被调用
                return null;
            }).when(detailParser).parseWithBatchProcessor(eq(tempFile), any());

            List<AlipayCsvData> billDetails = parser.parseBillDetails(tempFile);
            assertNotNull(billDetails);
            // 由于是Mock，返回空列表，我们主要测试不抛异常

            // 创建模拟数据用于后续测试
            AlipayCsvData mockDetail = new AlipayCsvData();
            mockDetail.setTradeTime("2025/6/27 20:00");
            mockDetail.setTradeCategory("餐饮美食");
            mockDetail.setCounterparty("测试商户");
            mockDetail.setInOrOut("支出");
            mockDetail.setAmount("50.00");
            billDetails = List.of(mockDetail);

            System.out.println("模拟的账单明细数量: " + billDetails.size());

            // 4. 测试数据转换 - Mock转换器
            AccountBookItemCreateReqDTO mockItem = new AccountBookItemCreateReqDTO();
            mockItem.setUserId(1L);
            mockItem.setType(false); // 支出
            mockItem.setSourceType("导入");
            List<AccountBookItemCreateReqDTO> mockAccountBookItems = List.of(mockItem);

            when(itemConverter.convert(eq(billDetails), eq(1L), eq(1L))).thenReturn(mockAccountBookItems);

            List<AccountBookItemCreateReqDTO> accountBookItems =
                parser.convertToAccountBookItems(billDetails, 1L, 1L);
            assertNotNull(accountBookItems);
            assertEquals(billDetails.size(), accountBookItems.size());

            // 验证转换后的数据
            AccountBookItemCreateReqDTO firstItem = accountBookItems.get(0);
            assertNotNull(firstItem.getUserId());
            assertNotNull(firstItem.getType());
            assertNotNull(firstItem.getSourceType());

            System.out.println("转换后的账本项目数量: " + accountBookItems.size());

        } finally {
            // 清理临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    @Test
    public void testCanParseMethod() throws IOException {
        // 创建临时文件进行测试
        File alipayFile1 = Files.createTempFile("支付宝交易明细", ".csv").toFile();
        File alipayFile2 = Files.createTempFile("alipay_bill", ".csv").toFile();
        File nonAlipayFile = Files.createTempFile("weixin_bill", ".csv").toFile();
        File txtFile = Files.createTempFile("test", ".txt").toFile();

        try {
            // 测试支付宝文件识别
            assertTrue(parser.canParse(alipayFile1));
            assertTrue(parser.canParse(alipayFile2));

            // 测试非支付宝文件
            assertFalse(parser.canParse(nonAlipayFile));
            assertFalse(parser.canParse(txtFile));

            // 测试null文件
            assertFalse(parser.canParse(null));
        } finally {
            // 清理临时文件
            alipayFile1.delete();
            alipayFile2.delete();
            nonAlipayFile.delete();
            txtFile.delete();
        }
    }

    /**
     * 创建临时的支付宝账单文件用于测试
     */
    private File createTempAlipayBillFile() throws IOException {
        File tempFile = Files.createTempFile("test_alipay_bill", ".csv").toFile();
        
        // 写入支付宝账单格式的测试数据
        try (FileWriter writer = new FileWriter(tempFile, java.nio.charset.Charset.forName("GBK"))) {
            // 写入头部信息（前24行）
            writer.write("------------------------------------------------------------------------------------,,,,,,,,,,,\n");
            writer.write("导出信息：,,,,,,,,,,,\n");
            writer.write("姓名：测试用户,,,,,,,,,,,\n");
            writer.write("支付宝账户：<EMAIL>,,,,,,,,,,,\n");
            writer.write("起始时间：[2025-05-28 00:00:00]    终止时间：[2025-06-28 23:59:59],,,,,,,,,,,\n");
            writer.write("导出交易类型：[全部],,,,,,,,,,,\n");
            writer.write("导出时间：[2025-06-28 12:03:23],,,,,,,,,,,\n");
            writer.write("共3笔记录,,,,,,,,,,,\n");
            writer.write("收入：1笔 100.00元,,,,,,,,,,,\n");
            writer.write("支出：2笔 150.00元,,,,,,,,,,,\n");
            writer.write("不计收支：0笔 0.00元,,,,,,,,,,,\n");
            writer.write(",,,,,,,,,,,\n");
            writer.write("特别提示：,,,,,,,,,,,\n");
            writer.write("1.本回单内容可表明支付宝受理了相应支付交易申请,,,,,,,,,,,\n");
            writer.write("2.请勿将本回单作为收款方发货的凭据使用,,,,,,,,,,,\n");
            writer.write("3.支付宝快捷支付等非余额支付方式可能既产生支付宝交易也同步产生银行交易,,,,,,,,,,,\n");
            writer.write("4.本回单如经任何涂改、编造，均立即失去效力,,,,,,,,,,,\n");
            writer.write("5.部分账单如：充值提现、账户转存或者个人设置收支等不计入为收入或者支出,,,,,,,,,,,\n");
            writer.write("6.因统计逻辑不同，明细金额直接累加后，可能会和下方统计金额不一致,,,,,,,,,,,\n");
            writer.write("7.禁止将本回单用于非法用途,,,,,,,,,,,\n");
            writer.write("8.本明细仅展示当前账单中的交易，不包括已删除的记录,,,,,,,,,,,\n");
            writer.write("9.本明细仅供个人对账使用。,,,,,,,,,,,\n");
            writer.write(",,,,,,,,,,,\n");
            writer.write("------------------------支付宝（中国）网络技术有限公司  电子客户回单------------------------,,,,,,,,,,,\n");
            
            // 写入表头（第25行）
            writer.write("交易时间,交易分类,交易对方,对方账号,商品说明,收/支,金额,收/付款方式,交易状态,交易订单号,商家订单号,备注\n");
            
            // 写入测试数据
            writer.write("2025/6/27 20:00,餐饮美食,测试商户1,<EMAIL>,测试商品1,支出,50.00,余额,交易成功,202506270001,M202506270001,测试备注1\n");
            writer.write("2025/6/26 15:30,转账,朋友转账,<EMAIL>,朋友转账,收入,100.00,余额,交易成功,202506260001,M202506260001,\n");
            writer.write("2025/6/25 10:15,生活服务,测试商户2,<EMAIL>,测试服务,支出,100.00,花呗,交易成功,202506250001,M202506250001,测试备注2\n");
        }
        
        return tempFile;
    }
}
