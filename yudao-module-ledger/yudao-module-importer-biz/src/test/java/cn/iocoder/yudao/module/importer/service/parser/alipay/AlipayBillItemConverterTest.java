package cn.iocoder.yudao.module.importer.service.parser.alipay;

import cn.iocoder.yudao.framework.test.core.ut.BaseMockitoUnitTest;
import cn.iocoder.yudao.module.ledger.api.accountbook.dto.AccountBookItemCreateReqDTO;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AlipayBillItemConverter 单元测试
 *
 * <AUTHOR>
 */
public class AlipayBillItemConverterTest extends BaseMockitoUnitTest {

    @InjectMocks
    private AlipayBillItemConverter converter;

    @Test
    public void testConvertSingleExpenseRecord() {
        // 准备测试数据 - 支出记录
        AlipayCsvData csvData = new AlipayCsvData();
        csvData.setTradeTime("2025/6/27 20:00");
        csvData.setTradeCategory("餐饮美食");
        csvData.setCounterparty("钱大妈");
        csvData.setCounterpartyAccount("qin***@qdama.cn");
        csvData.setGoodsDescription("花苦瓜等4件商品");
        csvData.setInOrOut("支出");
        csvData.setAmount("43.36");
        csvData.setPaymentMethod("江苏银行信用购(原花呗)&红包");
        csvData.setTradeStatus("交易成功");
        csvData.setTradeOrderNumber("2025062722001425231402209413");
        csvData.setMerchantOrderNumber("909170904049684480");
        csvData.setRemark("");
        
        Long billFileId = 123L;
        Long userId = 456L;
        
        // 执行转换
        List<AccountBookItemCreateReqDTO> result = converter.convert(Arrays.asList(csvData), billFileId, userId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        AccountBookItemCreateReqDTO item = result.get(0);
        assertEquals(userId, item.getUserId());
        assertEquals(String.valueOf(billFileId), item.getAccountBookId());
        assertEquals(false, item.getType()); // 支出
        assertEquals(new BigDecimal("43.36"), item.getAmount());
        assertEquals("餐饮美食", item.getCategoryId());
        assertEquals("导入", item.getSourceType());
        assertEquals(String.valueOf(billFileId), item.getSourceId());
        
        // 验证时间转换
        assertNotNull(item.getOccurredTime());
        
        // 验证备注信息
        String remark = item.getRemark();
        assertNotNull(remark);
        assertTrue(remark.contains("钱大妈"));
        assertTrue(remark.contains("花苦瓜等4件商品"));
        assertTrue(remark.contains("江苏银行信用购(原花呗)&红包"));
    }

    @Test
    public void testConvertSingleIncomeRecord() {
        // 准备测试数据 - 收入记录
        AlipayCsvData csvData = new AlipayCsvData();
        csvData.setTradeTime("2025/6/25 15:30");
        csvData.setTradeCategory("转账");
        csvData.setCounterparty("朋友转账");
        csvData.setInOrOut("收入");
        csvData.setAmount("100.00");
        csvData.setPaymentMethod("余额");
        csvData.setTradeStatus("交易成功");
        
        Long billFileId = 123L;
        Long userId = 456L;
        
        // 执行转换
        List<AccountBookItemCreateReqDTO> result = converter.convert(Arrays.asList(csvData), billFileId, userId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        AccountBookItemCreateReqDTO item = result.get(0);
        assertEquals(true, item.getType()); // 收入
        assertEquals(new BigDecimal("100.00"), item.getAmount());
    }

    @Test
    public void testConvertMultipleRecords() {
        // 准备多条测试数据
        AlipayCsvData csvData1 = createTestCsvData("2025/6/27 20:00", "餐饮美食", "支出", "43.36");
        AlipayCsvData csvData2 = createTestCsvData("2025/6/26 11:54", "保险", "支出", "77.85");
        AlipayCsvData csvData3 = createTestCsvData("2025/6/25 15:30", "转账", "收入", "100.00");
        
        List<AlipayCsvData> csvDataList = Arrays.asList(csvData1, csvData2, csvData3);
        
        // 执行转换
        List<AccountBookItemCreateReqDTO> result = converter.convert(csvDataList, 123L, 456L);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // 验证第一条记录（支出）
        AccountBookItemCreateReqDTO item1 = result.get(0);
        assertEquals(false, item1.getType());
        assertEquals(new BigDecimal("43.36"), item1.getAmount());
        
        // 验证第二条记录（支出）
        AccountBookItemCreateReqDTO item2 = result.get(1);
        assertEquals(false, item2.getType());
        assertEquals(new BigDecimal("77.85"), item2.getAmount());
        
        // 验证第三条记录（收入）
        AccountBookItemCreateReqDTO item3 = result.get(2);
        assertEquals(true, item3.getType());
        assertEquals(new BigDecimal("100.00"), item3.getAmount());
    }

    @Test
    public void testConvertWithEmptyAmount() {
        // 测试空金额的处理
        AlipayCsvData csvData = createTestCsvData("2025/6/27 20:00", "测试", "支出", "");
        
        List<AccountBookItemCreateReqDTO> result = converter.convert(Arrays.asList(csvData), 123L, 456L);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(BigDecimal.ZERO, result.get(0).getAmount());
    }

    @Test
    public void testConvertWithInvalidAmount() {
        // 测试无效金额的处理
        AlipayCsvData csvData = createTestCsvData("2025/6/27 20:00", "测试", "支出", "invalid_amount");
        
        List<AccountBookItemCreateReqDTO> result = converter.convert(Arrays.asList(csvData), 123L, 456L);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(BigDecimal.ZERO, result.get(0).getAmount());
    }

    @Test
    public void testConvertWithUnknownInOrOut() {
        // 测试未知收支类型的处理
        AlipayCsvData csvData = createTestCsvData("2025/6/27 20:00", "测试", "未知", "50.00");
        
        List<AccountBookItemCreateReqDTO> result = converter.convert(Arrays.asList(csvData), 123L, 456L);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(false, result.get(0).getType()); // 默认为支出
    }

    @Test
    public void testConvertWithInvalidDateTime() {
        // 测试无效日期时间的处理
        AlipayCsvData csvData = createTestCsvData("invalid_date", "测试", "支出", "50.00");
        
        List<AccountBookItemCreateReqDTO> result = converter.convert(Arrays.asList(csvData), 123L, 456L);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNotNull(result.get(0).getOccurredTime()); // 应该使用当前时间
    }

    @Test
    public void testConvertEmptyList() {
        // 测试空列表的处理
        List<AccountBookItemCreateReqDTO> result = converter.convert(Arrays.asList(), 123L, 456L);
        
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * 创建测试用的CSV数据
     */
    private AlipayCsvData createTestCsvData(String tradeTime, String category, String inOrOut, String amount) {
        AlipayCsvData csvData = new AlipayCsvData();
        csvData.setTradeTime(tradeTime);
        csvData.setTradeCategory(category);
        csvData.setCounterparty("测试商户");
        csvData.setCounterpartyAccount("<EMAIL>");
        csvData.setGoodsDescription("测试商品");
        csvData.setInOrOut(inOrOut);
        csvData.setAmount(amount);
        csvData.setPaymentMethod("测试支付方式");
        csvData.setTradeStatus("交易成功");
        csvData.setTradeOrderNumber("test_order_123");
        csvData.setMerchantOrderNumber("merchant_order_456");
        csvData.setRemark("测试备注");
        return csvData;
    }
}
