package cn.iocoder.yudao.module.importer.service.parser.alipay;

import cn.iocoder.yudao.framework.test.core.ut.BaseMockitoUnitTest;
import cn.iocoder.yudao.module.importer.config.BillParsingConfig;
import cn.iocoder.yudao.module.importer.service.parser.BatchProcessor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * AlipayBillDetailParser 集成测试
 * 使用真实的CSV数据进行测试
 *
 * <AUTHOR>
 */
public class AlipayBillDetailParserIntegrationTest extends BaseMockitoUnitTest {

    @InjectMocks
    private AlipayBillDetailParser parser;

    @Mock
    private BillParsingConfig billParsingConfig;

    @BeforeEach
    public void setUp() {
        // 设置支付宝配置
        BillParsingConfig.AlipayConfig alipayConfig = new BillParsingConfig.AlipayConfig();
        alipayConfig.setCharset("GBK");
        alipayConfig.setHeaderRowNum(25);
        alipayConfig.setBatchSize(500);

        when(billParsingConfig.getAlipay()).thenReturn(alipayConfig);
    }

    @Test
    public void testParseRealAlipayBillData() throws IOException {
        // 创建临时的支付宝账单文件
        File tempFile = createTempAlipayBillFile();
        
        try {
            // 创建批处理器收集数据
            List<AlipayCsvData> collectedData = new ArrayList<>();
            BatchProcessor<AlipayCsvData> processor = new BatchProcessor<AlipayCsvData>() {
                @Override
                public void processBatch(List<AlipayCsvData> batch) {
                    collectedData.addAll(batch);
                }
            };

            // 执行解析
            assertDoesNotThrow(() -> {
                parser.parseWithBatchProcessor(tempFile, processor);
            });

            // 验证解析结果
            assertNotNull(collectedData);
            assertTrue(collectedData.size() > 0, "应该解析出至少一条记录");
            
            // 验证第一条记录的数据
            if (!collectedData.isEmpty()) {
                AlipayCsvData firstRecord = collectedData.get(0);
                assertNotNull(firstRecord.getTradeTime(), "交易时间不应为空");
                assertNotNull(firstRecord.getTradeCategory(), "交易分类不应为空");
                assertNotNull(firstRecord.getInOrOut(), "收支类型不应为空");
                assertNotNull(firstRecord.getAmount(), "金额不应为空");
                
                System.out.println("解析的第一条记录:");
                System.out.println("交易时间: " + firstRecord.getTradeTime());
                System.out.println("交易分类: " + firstRecord.getTradeCategory());
                System.out.println("交易对方: " + firstRecord.getCounterparty());
                System.out.println("收/支: " + firstRecord.getInOrOut());
                System.out.println("金额: " + firstRecord.getAmount());
                
                // 验证数据格式
                assertTrue(firstRecord.getInOrOut().equals("收入") || 
                          firstRecord.getInOrOut().equals("支出") || 
                          firstRecord.getInOrOut().equals("不计收支"),
                          "收支类型应为：收入、支出或不计收支");
            }
            
            System.out.println("总共解析了 " + collectedData.size() + " 条记录");
            
        } finally {
            // 清理临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 创建临时的支付宝账单文件用于测试
     */
    private File createTempAlipayBillFile() throws IOException {
        File tempFile = Files.createTempFile("test_alipay_bill", ".csv").toFile();
        
        // 写入支付宝账单格式的测试数据
        try (FileWriter writer = new FileWriter(tempFile, java.nio.charset.Charset.forName("GBK"))) {
            // 写入头部信息（前24行）
            writer.write("------------------------------------------------------------------------------------,,,,,,,,,,,\n");
            writer.write("导出信息：,,,,,,,,,,,\n");
            writer.write("姓名：测试用户,,,,,,,,,,,\n");
            writer.write("支付宝账户：<EMAIL>,,,,,,,,,,,\n");
            writer.write("起始时间：[2025-05-28 00:00:00]    终止时间：[2025-06-28 23:59:59],,,,,,,,,,,\n");
            writer.write("导出交易类型：[全部],,,,,,,,,,,\n");
            writer.write("导出时间：[2025-06-28 12:03:23],,,,,,,,,,,\n");
            writer.write("共3笔记录,,,,,,,,,,,\n");
            writer.write("收入：1笔 100.00元,,,,,,,,,,,\n");
            writer.write("支出：2笔 150.00元,,,,,,,,,,,\n");
            writer.write("不计收支：0笔 0.00元,,,,,,,,,,,\n");
            writer.write(",,,,,,,,,,,\n");
            writer.write("特别提示：,,,,,,,,,,,\n");
            writer.write("1.本回单内容可表明支付宝受理了相应支付交易申请,,,,,,,,,,,\n");
            writer.write("2.请勿将本回单作为收款方发货的凭据使用,,,,,,,,,,,\n");
            writer.write("3.支付宝快捷支付等非余额支付方式可能既产生支付宝交易也同步产生银行交易,,,,,,,,,,,\n");
            writer.write("4.本回单如经任何涂改、编造，均立即失去效力,,,,,,,,,,,\n");
            writer.write("5.部分账单如：充值提现、账户转存或者个人设置收支等不计入为收入或者支出,,,,,,,,,,,\n");
            writer.write("6.因统计逻辑不同，明细金额直接累加后，可能会和下方统计金额不一致,,,,,,,,,,,\n");
            writer.write("7.禁止将本回单用于非法用途,,,,,,,,,,,\n");
            writer.write("8.本明细仅展示当前账单中的交易，不包括已删除的记录,,,,,,,,,,,\n");
            writer.write("9.本明细仅供个人对账使用。,,,,,,,,,,,\n");
            writer.write(",,,,,,,,,,,\n");
            writer.write("------------------------支付宝（中国）网络技术有限公司  电子客户回单------------------------,,,,,,,,,,,\n");
            
            // 写入表头（第25行）
            writer.write("交易时间,交易分类,交易对方,对方账号,商品说明,收/支,金额,收/付款方式,交易状态,交易订单号,商家订单号,备注\n");
            
            // 写入测试数据
            writer.write("2025/6/27 20:00,餐饮美食,测试商户1,<EMAIL>,测试商品1,支出,50.00,余额,交易成功,202506270001,M202506270001,测试备注1\n");
            writer.write("2025/6/26 15:30,转账,朋友转账,<EMAIL>,朋友转账,收入,100.00,余额,交易成功,202506260001,M202506260001,\n");
            writer.write("2025/6/25 10:15,生活服务,测试商户2,<EMAIL>,测试服务,支出,100.00,花呗,交易成功,202506250001,M202506250001,测试备注2\n");
        }
        
        return tempFile;
    }
}
