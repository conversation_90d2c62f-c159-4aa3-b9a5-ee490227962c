package cn.iocoder.yudao.module.importer.service.importtask;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.importer.dal.dataobject.importtask.ImportTaskDO;
import cn.iocoder.yudao.module.importer.dal.mysql.importtask.ImportTaskMapper;
import cn.iocoder.yudao.module.importer.enums.ImportTaskStatusEnum;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import jakarta.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link ImportTaskServiceImpl} 的单元测试类
 */
@Import(ImportTaskServiceImpl.class)
public class ImportTaskServiceTest extends BaseDbUnitTest {

    @Resource
    private ImportTaskService importTaskService;

    @Resource
    private ImportTaskMapper importTaskMapper;

    @Test
    public void testCreateImportTask() {
        // 准备参数
        Long userId = 1L;
        Long accountBookId = 100L;

        // 调用
        String taskId = importTaskService.createImportTask(userId, accountBookId);

        // 断言
        assertNotNull(taskId);
        assertFalse(taskId.isEmpty());

        // 验证数据库记录
        ImportTaskDO task = importTaskMapper.selectByTaskId(taskId);
        assertNotNull(task);
        assertEquals(userId, task.getUserId());
        assertEquals(accountBookId, task.getAccountBookId());
        assertEquals(ImportTaskStatusEnum.AWAITING_EMAIL.getStatus(), task.getStatus());
        assertNotNull(task.getUniqueEmail());
        assertTrue(task.getUniqueEmail().contains(taskId));
    }

    @Test
    public void testGetByTaskId() {
        // 准备参数和数据
        String taskId = importTaskService.createImportTask(1L, 100L);

        // 调用
        ImportTaskDO result = importTaskService.getByTaskId(taskId);

        // 断言
        assertNotNull(result);
        assertEquals(taskId, result.getTaskId());
    }

    @Test
    public void testGetByUniqueEmail() {
        // 准备参数和数据
        String taskId = importTaskService.createImportTask(1L, 100L);
        ImportTaskDO task = importTaskService.getByTaskId(taskId);

        // 调用
        ImportTaskDO result = importTaskService.getByUniqueEmail(task.getUniqueEmail());

        // 断言
        assertNotNull(result);
        assertEquals(taskId, result.getTaskId());
        assertEquals(task.getUniqueEmail(), result.getUniqueEmail());
    }

    @Test
    public void testUpdateTaskStatus() {
        // 准备参数和数据
        String taskId = importTaskService.createImportTask(1L, 100L);

        // 调用
        importTaskService.updateTaskStatus(taskId, ImportTaskStatusEnum.AWAITING_PASSWORD.getStatus());

        // 断言
        ImportTaskDO task = importTaskService.getByTaskId(taskId);
        assertEquals(ImportTaskStatusEnum.AWAITING_PASSWORD.getStatus(), task.getStatus());
    }

    @Test
    public void testUpdateTaskStatusAndFile() {
        // 准备参数和数据
        String taskId = importTaskService.createImportTask(1L, 100L);
        String filePath = "/test/file/path.zip";
        String fileName = "test.zip";

        // 调用
        importTaskService.updateTaskStatusAndFile(taskId, 
                ImportTaskStatusEnum.AWAITING_PASSWORD.getStatus(), filePath, fileName);

        // 断言
        ImportTaskDO task = importTaskService.getByTaskId(taskId);
        assertEquals(ImportTaskStatusEnum.AWAITING_PASSWORD.getStatus(), task.getStatus());
        assertEquals(filePath, task.getFilePath());
        assertEquals(fileName, task.getFileName());
    }

    @Test
    public void testUpdateUnzipPassword() {
        // 准备参数和数据
        String taskId = importTaskService.createImportTask(1L, 100L);
        String password = "123456";

        // 调用
        importTaskService.updateUnzipPassword(taskId, password);

        // 断言
        ImportTaskDO task = importTaskService.getByTaskId(taskId);
        assertEquals(password, task.getUnzipPassword());
    }

    @Test
    public void testUpdateTaskCompleted() {
        // 准备参数和数据
        String taskId = importTaskService.createImportTask(1L, 100L);
        Integer processedRecords = 100;
        String errorMessage = null;

        // 调用
        importTaskService.updateTaskCompleted(taskId, 
                ImportTaskStatusEnum.COMPLETED.getStatus(), processedRecords, errorMessage);

        // 断言
        ImportTaskDO task = importTaskService.getByTaskId(taskId);
        assertEquals(ImportTaskStatusEnum.COMPLETED.getStatus(), task.getStatus());
        assertEquals(processedRecords, task.getProcessedRecords());
        assertEquals(errorMessage, task.getErrorMessage());
        assertNotNull(task.getProcessEndTime());
    }

}