# 邮件导入服务安全配置指南

## 安全问题修复

### 原始问题
原始的签名验证服务存在以下安全问题：
1. **弱默认密钥**：使用 `default-secret-key` 作为默认值
2. **缺少密钥强度验证**：没有检查密钥的安全性
3. **时序攻击风险**：使用普通字符串比较可能泄露签名信息
4. **载荷拼接攻击**：简单的字符串拼接可能被攻击

### 修复措施

#### 1. 强制密钥配置
```yaml
# application.yaml 或环境变量
mail-server:
  secret-key: ${MAIL_SERVER_SECRET_KEY:}  # 必须通过环境变量配置
  signature:
    timestamp-tolerance-seconds: 30       # 时间戳容忍度（秒）
    algorithm: HmacSHA256                 # 签名算法
```

#### 2. 密钥强度要求
- **最小长度**：32位字符
- **禁止弱密钥**：不能包含 default、test、123456、password、secret、key 等常见词汇
- **推荐生成方式**：
  ```bash
  # 生成64位随机密钥
  openssl rand -hex 32
  
  # 或使用 Python
  python3 -c "import secrets; print(secrets.token_hex(32))"
  ```

#### 3. 环境变量配置
```bash
# 生产环境
export MAIL_SERVER_SECRET_KEY="your-64-character-random-hex-key-here"

# Docker 环境
docker run -e MAIL_SERVER_SECRET_KEY="your-key" your-app

# Kubernetes
apiVersion: v1
kind: Secret
metadata:
  name: mail-server-secret
data:
  secret-key: <base64-encoded-key>
```

## 安全特性

### 1. 密钥验证
- 启动时自动验证密钥配置
- 检查密钥长度和强度
- 拒绝常见弱密钥模式

### 2. 防时序攻击
- 使用常量时间字符串比较
- 防止通过响应时间推断签名信息

### 3. 防拼接攻击
- 在载荷中添加分隔符 `|`
- 防止 `timestamp + data` 的拼接攻击

### 4. 时间戳验证
- 可配置的时间窗口（默认30秒）
- 防止重放攻击

## 使用示例

### 正确的签名生成（客户端）
```java
public String generateSignature(String timestamp, String data, String secretKey) {
    String payload = timestamp + "|" + data;
    HMac hMac = new HMac(HmacAlgorithm.HmacSHA256, secretKey.getBytes(StandardCharsets.UTF_8));
    return HexUtil.encodeHexStr(hMac.digest(payload));
}
```

### HTTP 请求示例
```http
POST /ledger/import/receive-raw-email
Content-Type: application/json

{
  "signature": "calculated-hmac-sha256-signature",
  "timestamp": "1634567890000",
  "rawEmailData": "email-content"
}
```

## 部署建议

### 1. 密钥管理
- 使用密钥管理服务（如 AWS KMS、Azure Key Vault）
- 定期轮换密钥
- 不要在代码或配置文件中硬编码密钥

### 2. 监控和告警
- 监控签名验证失败率
- 设置异常告警
- 记录安全事件日志

### 3. 网络安全
- 使用 HTTPS 传输
- 配置防火墙规则
- 限制访问来源 IP

## 故障排除

### 常见错误
1. **密钥未配置**：`邮件服务器签名密钥未配置`
2. **密钥强度不足**：`邮件服务器签名密钥强度不足`
3. **签名验证失败**：检查密钥配置和载荷格式
4. **时间戳过期**：检查系统时间同步

### 调试步骤
1. 确认密钥配置正确
2. 验证时间戳格式（毫秒级）
3. 检查载荷构建格式：`timestamp|data`
4. 确认使用 HmacSHA256 算法
5. 检查字符编码（UTF-8）

## 安全检查清单

- [ ] 配置了强密钥（至少32位）
- [ ] 密钥通过环境变量配置
- [ ] 启用了时间戳验证
- [ ] 使用 HTTPS 传输
- [ ] 配置了监控和告警
- [ ] 定期审查访问日志
- [ ] 制定了密钥轮换计划
