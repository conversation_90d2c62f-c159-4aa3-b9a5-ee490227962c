# 邮件导入服务部署安全配置指南

## 🔐 密钥配置最佳实践

### 1. 本地开发环境

#### 方案一：使用 application-local.yaml（推荐）
```yaml
# yudao-server/src/main/resources/application-local.yaml
mail-server:
  secret-key: "dev-test-key-a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"
  signature:
    timestamp-tolerance-seconds: 60  # 本地开发可以放宽到60秒便于调试
    algorithm: HmacSHA256
```

#### 方案二：使用环境变量
```bash
# 在 ~/.bashrc 或 ~/.zshrc 中添加
export MAIL_SERVER_SECRET_KEY="dev-test-key-a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"

# 然后在配置文件中引用
mail-server:
  secret-key: ${MAIL_SERVER_SECRET_KEY}
```

#### 方案三：使用 .env 文件（需要额外配置）
```bash
# 项目根目录创建 .env 文件（记得加入 .gitignore）
MAIL_SERVER_SECRET_KEY=dev-test-key-a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456
```

### 2. 测试环境

#### application-dev.yaml
```yaml
mail-server:
  secret-key: ${MAIL_SERVER_SECRET_KEY}  # 通过环境变量注入
  signature:
    timestamp-tolerance-seconds: 30
    algorithm: HmacSHA256
```

#### 环境变量配置
```bash
# 在测试服务器上设置
export MAIL_SERVER_SECRET_KEY="test-env-key-xyz789abc123def456ghi789jkl012mno345pqr678stu901vwx234yz567"
```

### 3. 生产环境

#### application-prod.yaml
```yaml
mail-server:
  secret-key: ${MAIL_SERVER_SECRET_KEY}  # 必须通过环境变量或密钥管理服务
  signature:
    timestamp-tolerance-seconds: 30      # 生产环境使用严格的30秒窗口
    algorithm: HmacSHA256
```

#### 密钥管理方案

**方案一：环境变量**
```bash
# 在生产服务器上设置（通过部署脚本或容器编排工具）
export MAIL_SERVER_SECRET_KEY="prod-secret-key-generated-by-secure-random-generator"
```

**方案二：Docker Secrets**
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    image: your-app:latest
    secrets:
      - mail_server_secret
    environment:
      - MAIL_SERVER_SECRET_KEY_FILE=/run/secrets/mail_server_secret

secrets:
  mail_server_secret:
    file: ./secrets/mail_server_secret.txt
```

**方案三：Kubernetes Secrets**
```yaml
# k8s-secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: mail-server-secret
type: Opaque
data:
  secret-key: <base64-encoded-secret-key>

---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
spec:
  template:
    spec:
      containers:
      - name: app
        env:
        - name: MAIL_SERVER_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: mail-server-secret
              key: secret-key
```

**方案四：云服务密钥管理**
```bash
# AWS Systems Manager Parameter Store
aws ssm put-parameter \
  --name "/app/mail-server/secret-key" \
  --value "your-secret-key" \
  --type "SecureString"

# 在应用中通过 AWS SDK 获取
```

## 🛡️ 安全检查清单

### Git 安全
- [ ] 确保 `application-local.yaml` 中的测试密钥不会泄露到生产环境
- [ ] 将包含敏感信息的文件添加到 `.gitignore`
- [ ] 定期检查 Git 历史，确保没有意外提交密钥

### 密钥管理
- [ ] 生产环境密钥至少64位随机字符
- [ ] 不同环境使用不同的密钥
- [ ] 定期轮换密钥（建议每季度）
- [ ] 密钥访问权限最小化

### 监控和审计
- [ ] 监控签名验证失败率
- [ ] 记录异常的签名验证尝试
- [ ] 设置密钥泄露检测告警

## 🔧 密钥生成工具

### 生成强密钥
```bash
# 方法1：使用 OpenSSL
openssl rand -hex 32

# 方法2：使用 Python
python3 -c "import secrets; print(secrets.token_hex(32))"

# 方法3：使用 Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# 方法4：在线生成（仅用于开发环境）
# https://www.random.org/strings/
```

### 验证密钥强度
```bash
# 检查密钥长度
echo "your-key" | wc -c

# 检查密钥熵（Linux/macOS）
echo "your-key" | ent
```

## 📁 .gitignore 配置

确保以下文件不会被提交到 Git：

```gitignore
# 环境配置文件
.env
.env.local
.env.*.local

# 密钥文件
secrets/
*.key
*.pem

# 本地配置覆盖
application-local-override.yaml
config/local/
```

## 🚀 部署脚本示例

### 本地开发启动脚本
```bash
#!/bin/bash
# start-local.sh

# 设置本地开发环境变量
export SPRING_PROFILES_ACTIVE=local
export MAIL_SERVER_SECRET_KEY="dev-test-key-a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"

# 启动应用
java -jar target/your-app.jar
```

### 生产环境部署脚本
```bash
#!/bin/bash
# deploy-prod.sh

# 从密钥管理服务获取密钥
SECRET_KEY=$(aws ssm get-parameter --name "/app/mail-server/secret-key" --with-decryption --query "Parameter.Value" --output text)

# 设置环境变量
export SPRING_PROFILES_ACTIVE=prod
export MAIL_SERVER_SECRET_KEY="$SECRET_KEY"

# 启动应用
java -jar your-app.jar
```

## ⚠️ 安全注意事项

1. **永远不要在代码中硬编码密钥**
2. **不要在日志中输出密钥信息**
3. **使用不同环境的不同密钥**
4. **定期轮换密钥**
5. **监控密钥使用情况**
6. **限制密钥访问权限**

## 🔍 故障排除

### 常见问题
1. **密钥未配置**：检查环境变量或配置文件
2. **密钥格式错误**：确保是64位十六进制字符串
3. **环境变量未生效**：重启应用或检查环境变量设置
4. **权限问题**：检查密钥文件的读取权限
