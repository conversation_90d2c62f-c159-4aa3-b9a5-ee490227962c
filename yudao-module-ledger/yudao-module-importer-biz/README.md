# 邮件导入账单系统技术设计文档

## 项目概述

本系统实现了一个基于邮件的账单导入功能，用户可以通过发送邮件的方式上传账单文件，系统自动解析并导入到账本系统中。整个流程采用异步处理架构，确保高性能和可靠性。

## 系统架构

### 核心组件

1. **任务管理模块** - 管理导入任务的生命周期
2. **邮件处理模块** - 接收和解析邮件
3. **签名验证模块** - 确保邮件来源的安全性
4. **密码验证模块** - 快速验证ZIP文件密码
5. **账单解析模块** - 解析不同格式的账单文件
6. **消息队列模块** - 异步处理各个环节

### 技术栈

- **框架**: Spring Boot 3.2 + YuDao框架
- **数据库**: MySQL + MyBatis Plus
- **消息队列**: Redis Stream
- **文件处理**: zip4j (ZIP解压)
- **邮件解析**: JavaMail API
- **文件存储**: OSS (对象存储服务)

## 业务流程

### 1. 任务创建流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant API as API接口
    participant TaskService as 任务服务
    participant DB as 数据库

    User->>API: POST /ledger/import/tasks
    API->>TaskService: createImportTask(userId, accountBookId)
    TaskService->>DB: 创建任务记录
    TaskService->>TaskService: 生成唯一邮箱地址
    TaskService-->>API: 返回taskId和uniqueEmail
    API-->>User: 返回邮箱地址
```

### 2. 邮件接收与处理流程

```mermaid
sequenceDiagram
    participant MailServer as 邮件服务器
    participant API as API接口
    participant Queue as Redis队列
    participant Consumer as 邮件消费者
    participant TaskService as 任务服务
    participant OSS as 对象存储

    MailServer->>API: POST /ledger/import/receive-raw-email
    API->>API: 验证签名
    API->>Queue: 推送到mail-preprocess队列
    API-->>MailServer: 返回200 OK
    
    Queue->>Consumer: 消费邮件预处理消息
    Consumer->>Consumer: 解析邮件，提取收件人和附件
    Consumer->>TaskService: 验证任务有效性
    Consumer->>OSS: 上传附件到对象存储
    Consumer->>TaskService: 更新任务状态为AWAITING_PASSWORD
```

### 3. 密码验证与账单解析流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant API as API接口
    participant PasswordService as 密码验证服务
    participant Queue as Redis队列
    participant Consumer as 账单解析消费者
    participant BillService as 账单解析服务
    participant AccountAPI as 账本API

    User->>API: POST /ledger/import/tasks/{taskId}/submit-password
    API->>PasswordService: 快速验证ZIP密码
    PasswordService-->>API: 验证结果
    API->>Queue: 推送到bill-parsing队列
    API-->>User: 返回验证成功
    
    Queue->>Consumer: 消费账单解析消息
    Consumer->>BillService: 下载完整文件并解析
    BillService->>BillService: 解压ZIP文件
    BillService->>BillService: 解析CSV账单数据
    BillService->>AccountAPI: 批量创建账本记录
    BillService->>Consumer: 更新任务状态为COMPLETED
```

## 数据库设计

### 导入任务表 (importer_import_task)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| task_id | varchar(64) | 任务唯一标识 |
| user_id | bigint | 用户ID |
| account_book_id | bigint | 账本ID |
| unique_email | varchar(255) | 生成的唯一邮箱地址 |
| status | tinyint | 任务状态 |
| file_path | varchar(512) | 文件存储路径 |
| file_name | varchar(255) | 文件名 |
| unzip_password | varchar(255) | 解压密码 |
| error_message | text | 错误信息 |
| process_start_time | datetime | 处理开始时间 |
| process_end_time | datetime | 处理结束时间 |
| processed_records | int | 处理的记录数 |

### 任务状态枚举

| 状态值 | 状态名 | 说明 |
|--------|--------|------|
| 1 | AWAITING_EMAIL | 等待邮件 |
| 2 | AWAITING_PASSWORD | 等待密码 |
| 3 | PROCESSING | 处理中 |
| 4 | COMPLETED | 已完成 |
| 5 | FAILED | 失败 |

## API接口设计

### 1. 创建导入任务

```http
POST /ledger/import/tasks
Content-Type: application/json

{
  "accountBookId": 1024
}
```

响应：
```json
{
  "code": 0,
  "data": {
    "taskId": "abc123def456",
    "uniqueEmail": "<EMAIL>"
  }
}
```

### 2. 接收原始邮件

```http
POST /ledger/import/receive-raw-email
Content-Type: application/json

{
  "signature": "签名字符串",
  "timestamp": "**********",
  "rawEmailData": "完整的邮件原始数据"
}
```

### 3. 提交解压密码

```http
POST /ledger/import/tasks/{taskId}/submit-password
Content-Type: application/json

{
  "password": "123456"
}
```

## 消息队列设计

### 1. 邮件预处理队列 (mail-preprocess)

**消息结构**:
```json
{
  "rawEmailData": "原始邮件数据"
}
```

**处理逻辑**:
- 解析邮件获取收件人地址
- 提取任务ID并验证任务有效性
- 提取邮件附件并上传到OSS
- 更新任务状态为AWAITING_PASSWORD

### 2. 账单解析队列 (bill-parsing)

**消息结构**:
```json
{
  "taskId": "任务ID",
  "s3Path": "文件路径",
  "password": "解压密码"
}
```

**处理逻辑**:
- 从OSS下载完整文件
- 使用密码解压ZIP文件
- 解析CSV格式的账单数据
- 批量创建账本记录
- 更新任务完成状态

## 安全设计

### 1. 签名验证机制

使用HMAC-SHA256算法验证邮件服务器请求的合法性：

```
签名计算: HMAC-SHA256(timestamp|rawEmailData, secretKey)
时间窗口: 30秒内有效
```

### 2. 唯一邮箱地址

每个导入任务生成唯一的邮箱地址：

```
格式: import-{taskId}@import.ledger.com
用途: 确保邮件路由到正确的任务
```

### 3. 快速密码验证

使用zip4j库的快速验证机制：
- 只下载ZIP文件头部分（前几KB）
- 验证密码能否打开文件索引
- 避免下载完整文件，提高响应速度

## 性能优化

### 1. 异步处理架构

- 邮件接收：立即响应200 OK，异步处理
- 密码验证：快速验证后推送到解析队列
- 账单解析：独立队列处理，避免阻塞

### 2. 文件处理优化

- 范围下载：仅下载文件头用于密码验证
- 临时文件管理：及时清理，避免磁盘空间浪费
- 批量处理：批量创建账本记录，提高数据库性能

### 3. 缓存策略

- 任务状态缓存：减少数据库查询
- 文件存储配置缓存：避免重复配置查询

## 错误处理

### 1. 邮件处理错误

- 签名验证失败：记录日志，返回失败响应
- 邮件解析失败：更新任务状态为FAILED
- 附件上传失败：重试机制 + 失败通知

### 2. 密码验证错误

- 密码错误：立即返回错误，用户可重新输入
- 文件损坏：更新任务状态为FAILED
- 网络异常：重试机制

### 3. 账单解析错误

- 文件格式错误：记录详细错误信息
- 数据转换失败：跳过错误记录，继续处理
- 批量插入失败：事务回滚，记录失败原因

## 监控与运维

### 1. 业务监控

- 任务创建成功率
- 邮件处理成功率
- 账单解析成功率
- 平均处理时间

### 2. 技术监控

- Redis队列积压情况
- 数据库连接池状态
- OSS存储使用情况
- 临时文件清理状态

### 3. 告警机制

- 队列积压告警
- 处理失败率过高告警
- 磁盘空间不足告警
- 数据库连接异常告警

## 部署说明

### 1. 环境配置

```yaml
# application.yaml
mail-server:
  secret-key: your-secret-key-here

spring:
  redis:
    host: localhost
    port: 6379
  datasource:
    url: *****************************************
```

### 2. 数据库初始化

执行SQL脚本创建导入任务表：
```sql
-- 见 src/test/resources/sql/create_import_task_table.sql
```

### 3. 权限配置

- OSS存储访问权限
- Redis访问权限
- 数据库连接权限

## 测试策略

### 1. 单元测试

- 服务层业务逻辑测试
- 签名验证算法测试
- 密码验证功能测试
- 消息队列消费者测试

### 2. 集成测试

- 完整导入流程测试
- 异常情况处理测试
- 并发处理能力测试

### 3. 性能测试

- 大文件处理性能测试
- 高并发场景测试
- 内存使用情况测试

## 未来扩展

### 1. 支持更多文件格式

- Excel格式账单
- PDF格式账单
- 其他银行格式支持

### 2. 智能识别功能

- 自动识别账单类型
- 智能字段映射
- 重复记录检测

### 3. 用户体验优化

- 实时处理进度推送
- 处理结果邮件通知
- 错误记录详细报告

---

## 总结

本系统通过异步消息队列架构，实现了高性能、高可靠性的邮件导入账单功能。系统具有良好的扩展性和维护性，能够满足大规模用户的使用需求。完善的错误处理和监控机制确保了系统的稳定运行。