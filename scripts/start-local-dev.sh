#!/bin/bash

# 本地开发环境启动脚本
# 用于设置邮件服务器安全配置和启动应用

set -e  # 遇到错误立即退出

echo "🚀 启动本地开发环境..."

# 设置 Spring Profile
export SPRING_PROFILES_ACTIVE=local

# 设置邮件服务器密钥（本地开发用）
# 注意：这是开发环境的测试密钥，请勿在生产环境使用
export MAIL_SERVER_SECRET_KEY="dev-test-key-a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"

echo "✅ 环境变量配置完成"
echo "   - SPRING_PROFILES_ACTIVE: $SPRING_PROFILES_ACTIVE"
echo "   - MAIL_SERVER_SECRET_KEY: ${MAIL_SERVER_SECRET_KEY:0:20}... (已设置)"

# 检查 Maven 是否可用
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven 未安装或不在 PATH 中"
    exit 1
fi

# 检查 Java 版本
JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
echo "☕ Java 版本: $JAVA_VERSION"

# 构建项目（跳过测试以加快启动速度）
echo "🔨 构建项目..."
mvn clean compile -DskipTests -q

# 启动应用
echo "🎯 启动应用..."
echo "📝 日志级别: DEBUG（便于开发调试）"
echo "⏰ 签名时间窗口: 60秒（本地开发放宽限制）"
echo ""

# 使用 Maven 启动，便于开发调试
mvn spring-boot:run -Dspring-boot.run.profiles=local

echo "🛑 应用已停止"
