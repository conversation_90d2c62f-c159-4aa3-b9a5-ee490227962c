# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Spring Boot multi-module Java application based on the "芋道 (YuDao)" framework - a comprehensive business management platform. The current version uses JDK 17 and Spring Boot 3.2.x.

**Key Architecture:**
- **Multi-module Maven project** with modular business domains
- **Spring Boot 3.2** with modern Java 17 features
- **MyBatis Plus** for database operations with MySQL support
- **Redis** for caching and session management
- **Spring Security** for authentication and authorization
- **Multi-tenant architecture** support (currently disabled)

## Development Commands

### Build and Run
```bash
# Clean and compile the project
mvn clean compile

# Run tests
mvn test

# Run a specific test class
mvn test -Dtest=ClassNameTest

# Package the application
mvn clean package

# Run the application (from yudao-server module)
mvn spring-boot:run -pl yudao-server

# Or run the JAR directly
java -jar yudao-server/target/yudao-server-*.jar
```

### Database Setup
The application requires MySQL database setup. Connection details are in:
- `yudao-server/src/main/resources/application-dev.yaml`
- Default connection: `*****************************************`

### Application Profiles
- **Local development**: `spring.profiles.active=local`
- **Development**: `spring.profiles.active=dev`
- **Server port**: 48080 (dev profile)

## Module Architecture

### Core Modules
- **yudao-dependencies**: Maven dependency management
- **yudao-framework**: Common framework components and Spring Boot starters
- **yudao-server**: Main application entry point (`YudaoServerApplication.java`)

### Business Modules
- **yudao-module-system**: System management (users, roles, permissions, tenants)
- **yudao-module-infra**: Infrastructure services (file upload, code generation, monitoring)
- **yudao-module-member**: Member/customer management
- **yudao-module-ledger**: Account book and financial ledger management
  - **yudao-module-ledger-biz**: Core ledger business logic
  - **yudao-module-importer-biz**: Bill import and parsing functionality

### Framework Components (yudao-framework)
- **yudao-common**: Common utilities and base classes
- **yudao-spring-boot-starter-web**: Web layer configuration
- **yudao-spring-boot-starter-mybatis**: Database and MyBatis configuration
- **yudao-spring-boot-starter-redis**: Redis cache configuration
- **yudao-spring-boot-starter-security**: Security and authentication
- **yudao-spring-boot-starter-test**: Test utilities and configuration

## Key Configuration

### Application Configuration
- Base package: `cn.iocoder.yudao`
- Main config files:
  - `application.yaml` (main configuration)
  - `application-dev.yaml` (development-specific)
  - `application-local.yaml` (local development)

### Database Configuration
- **Primary datasource**: MySQL with Druid connection pool
- **MyBatis Plus**: Automatic ID generation, logical delete support
- **Multi-datasource**: Configured but using single master database

### Testing
- **Test framework**: JUnit 5 + Mockito
- **Test configuration**: `application-unit-test.yaml`
- **In-memory database**: H2 for unit testing

## Bill Import and Parsing System

The project includes a sophisticated bill parsing system in `yudao-module-importer-biz`:

### Supported Formats
- **WeChat Pay**: CSV format with UTF-8 encoding
- **Alipay**: CSV format with GBK encoding  
- **ABC Bank**: CSV format for bank statements

### Key Components
- **BillParsingService**: Main service for parsing bill files
- **BillParserFactory**: Factory for creating format-specific parsers
- **Parser implementations**: Format-specific parsers (WeChat, Alipay, ABC Bank)
- **Batch processing**: Efficient processing of large bill files

### Configuration
```yaml
bill:
  parsing:
    enabled: true
    max-file-size: 50
    supported-file-types: [csv, xls, xlsx]
    weixin:
      charset: UTF-8
      header-row-num: 17
    alipay:
      charset: GBK
      header-row-num: 5
```

## Development Guidelines

### Code Style
- Follow standard Java naming conventions
- Use Lombok for reducing boilerplate code
- MapStruct for object mapping between layers
- Comprehensive logging with structured messages

### API Development
- **Admin APIs**: `/admin-api/**` - Management interface
- **App APIs**: `/app-api/**` - User-facing mobile/web APIs
- **Swagger documentation**: Available at `/swagger-ui` when running

### Testing Strategy
- Unit tests for service layer business logic
- Integration tests for repository layer
- Test data setup in `src/test/resources/sql/`

### Database Conventions
- Logical delete using `deleted` field (0=active, 1=deleted)
- Audit fields: `create_time`, `update_time`, `creator`, `updater`
- Tenant isolation support (when enabled)

## Common Tasks

### Adding a New Module
1. Create module directory structure following existing patterns
2. Add module to parent `pom.xml`
3. Implement API, BIZ layers with proper separation
4. Add database migration scripts if needed

### Running Tests
```bash
# Run all tests
mvn test

# Run tests for specific module
mvn test -pl yudao-module-ledger-biz

# Run with specific profile
mvn test -Dspring.profiles.active=unit-test
```

### Database Migrations
- SQL scripts in `/sql/mysql/` directory
- Test data setup in module-specific `src/test/resources/sql/`