# 环境变量配置示例文件
# 复制此文件为 .env 并填入实际值（.env 文件不会被提交到 Git）

# ==================== 邮件服务器安全配置 ====================

# 邮件服务器签名密钥
# 生产环境：使用 openssl rand -hex 32 生成64位随机密钥
# 开发环境：可以使用下面的测试密钥
MAIL_SERVER_SECRET_KEY=dev-test-key-a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456

# ==================== 数据库配置 ====================

# MySQL 数据库连接（如果需要覆盖默认配置）
# DB_HOST=localhost
# DB_PORT=3306
# DB_NAME=ruoyi-vue-pro
# DB_USERNAME=root
# DB_PASSWORD=123456

# ==================== Redis 配置 ====================

# Redis 连接（如果需要覆盖默认配置）
# REDIS_HOST=127.0.0.1
# REDIS_PORT=6379
# REDIS_PASSWORD=

# ==================== 其他敏感配置 ====================

# 微信相关密钥（如果需要）
# WX_MP_APP_ID=your-wechat-app-id
# WX_MP_SECRET=your-wechat-secret

# 支付相关密钥（如果需要）
# ALIPAY_APP_ID=your-alipay-app-id
# ALIPAY_PRIVATE_KEY=your-alipay-private-key

# ==================== 使用说明 ====================

# 1. 复制此文件为 .env
#    cp .env.example .env
#
# 2. 修改 .env 文件中的实际值
#
# 3. 在应用启动时，这些环境变量会被自动加载
#
# 4. 生产环境建议使用更安全的密钥管理方案
